@echo off
title Modern FiveM Server
echo.
echo ========================================
echo    Modern FiveM Server Starting...
echo ========================================
echo.

REM Check if FXServer exists
if not exist "FXServer.exe" (
    echo Error: FXServer.exe not found!
    echo Please download the FiveM server files from https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/
    echo Extract the files to this directory and run this script again.
    pause
    exit
)

REM Start the server
echo Starting FiveM Server...
echo.
FXServer.exe +exec server.cfg

REM If server stops, show message
echo.
echo ========================================
echo    Server has stopped!
echo ========================================
pause
