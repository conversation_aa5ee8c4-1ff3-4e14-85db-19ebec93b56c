Config = {}

-- Admin Panel Settings
Config.AdminKey = "F1"  -- Key to open admin panel
Config.AdminGroups = {
    "superadmin",
    "admin", 
    "moderator"
}

-- Admin Permissions
Config.Permissions = {
    superadmin = {
        "all"
    },
    admin = {
        "player.kick",
        "player.ban",
        "player.teleport",
        "player.spectate",
        "player.freeze",
        "player.heal",
        "player.revive",
        "player.setjob",
        "player.givemoney",
        "player.removemoney",
        "vehicle.spawn",
        "vehicle.delete",
        "vehicle.repair",
        "server.announce",
        "server.weather",
        "server.time",
        "logs.view"
    },
    moderator = {
        "player.kick",
        "player.teleport",
        "player.spectate",
        "player.freeze",
        "player.heal",
        "player.revive",
        "logs.view"
    }
}

-- Quick Actions
Config.QuickActions = {
    {
        name = "heal_all",
        label = "شفاء جميع اللاعبين",
        icon = "fas fa-heart",
        color = "#4CAF50",
        permission = "player.heal"
    },
    {
        name = "revive_all", 
        label = "إحياء جميع اللاعبين",
        icon = "fas fa-user-plus",
        color = "#2196F3",
        permission = "player.revive"
    },
    {
        name = "clear_chat",
        label = "مسح الدردشة",
        icon = "fas fa-broom",
        color = "#FF9800",
        permission = "server.announce"
    },
    {
        name = "restart_warning",
        label = "تحذير إعادة التشغيل",
        icon = "fas fa-exclamation-triangle",
        color = "#F44336",
        permission = "server.announce"
    }
}

-- Vehicle Categories
Config.VehicleCategories = {
    {
        name = "sports",
        label = "سيارات رياضية",
        vehicles = {
            {model = "adder", name = "Adder"},
            {model = "entityxf", name = "Entity XF"},
            {model = "nero", name = "Nero"},
            {model = "osiris", name = "Osiris"},
            {model = "t20", name = "T20"},
            {model = "turismor", name = "Turismo R"},
            {model = "zentorno", name = "Zentorno"}
        }
    },
    {
        name = "super",
        label = "سيارات خارقة",
        vehicles = {
            {model = "banshee2", name = "Banshee 900R"},
            {model = "bullet", name = "Bullet"},
            {model = "cheetah", name = "Cheetah"},
            {model = "entityxf", name = "Entity XF"},
            {model = "infernus", name = "Infernus"},
            {model = "vacca", name = "Vacca"},
            {model = "voltic", name = "Voltic"}
        }
    },
    {
        name = "motorcycles",
        label = "دراجات نارية",
        vehicles = {
            {model = "akuma", name = "Akuma"},
            {model = "bati", name = "Bati 801"},
            {model = "carbonrs", name = "Carbon RS"},
            {model = "hakuchou", name = "Hakuchou"},
            {model = "ruffian", name = "Ruffian"}
        }
    },
    {
        name = "emergency",
        label = "مركبات الطوارئ",
        vehicles = {
            {model = "police", name = "Police Cruiser"},
            {model = "police2", name = "Police Buffalo"},
            {model = "police3", name = "Police Interceptor"},
            {model = "ambulance", name = "Ambulance"},
            {model = "firetruk", name = "Fire Truck"}
        }
    }
}

-- Weather Types
Config.WeatherTypes = {
    {name = "CLEAR", label = "صافي"},
    {name = "EXTRASUNNY", label = "مشمس جداً"},
    {name = "CLOUDS", label = "غائم"},
    {name = "OVERCAST", label = "غائم جداً"},
    {name = "RAIN", label = "ممطر"},
    {name = "CLEARING", label = "يتحسن"},
    {name = "THUNDER", label = "عاصف"},
    {name = "SMOG", label = "ضبابي"},
    {name = "FOGGY", label = "ضباب كثيف"},
    {name = "XMAS", label = "ثلجي"},
    {name = "SNOWLIGHT", label = "ثلج خفيف"},
    {name = "BLIZZARD", label = "عاصفة ثلجية"}
}

-- Ban Reasons
Config.BanReasons = {
    "الغش/الهاك",
    "السلوك السيء",
    "عدم احترام القوانين",
    "التحرش",
    "الإعلانات المزعجة",
    "استغلال الأخطاء",
    "أخرى"
}

-- Kick Reasons  
Config.KickReasons = {
    "السلوك السيء",
    "عدم احترام القوانين", 
    "التحرش",
    "الإعلانات المزعجة",
    "تحذير",
    "أخرى"
}
