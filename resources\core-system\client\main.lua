-- Core Client System
local PlayerData = {}
local isLoggedIn = false

-- Initialize player data
RegisterNetEvent('core:playerLoaded')
AddEventHandler('core:playerLoaded', function(playerData)
    PlayerData = playerData
    isLoggedIn = true
    
    -- Trigger other resources that player is loaded
    TriggerEvent('core:onPlayerLoaded')
end)

-- Update money
RegisterNetEvent('core:updateMoney')
AddEventHandler('core:updateMoney', function(moneyType, amount)
    if PlayerData then
        PlayerData[moneyType] = amount
        TriggerEvent('hud:updateMoney', moneyType, amount)
    end
end)

-- Notification system
function ShowNotification(message, type, duration)
    type = type or "info"
    duration = duration or Config.NotificationDuration
    
    TriggerEvent('notifications:show', {
        message = message,
        type = type,
        duration = duration
    })
end

-- Get player data
function GetPlayerData()
    return PlayerData
end

-- Check if player is logged in
function IsPlayerLoggedIn()
    return isLoggedIn
end

-- Money functions
function GetMoney(moneyType)
    if PlayerData and PlayerData[moneyType] then
        return PlayerData[moneyType]
    end
    return 0
end

-- Spawn player
function SpawnPlayer(coords, heading)
    local playerPed = PlayerPedId()
    
    if coords then
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
    end
    
    if heading then
        SetEntityHeading(playerPed, heading)
    end
    
    -- Make sure player is visible and not frozen
    SetEntityVisible(playerPed, true, false)
    FreezeEntityPosition(playerPed, false)
    SetPlayerInvincible(PlayerId(), false)
    
    -- Clear any tasks
    ClearPedTasks(playerPed)
    
    -- Set health and armor
    SetEntityHealth(playerPed, 200)
    SetPedArmour(playerPed, 0)
end

-- Key mapping for phone (K key)
RegisterKeyMapping('openphone', 'Open Phone', 'keyboard', Config.PhoneKey)

RegisterCommand('openphone', function()
    if isLoggedIn then
        TriggerEvent('phone:toggle')
    end
end, false)

-- Disable some default GTA controls
CreateThread(function()
    while true do
        Wait(0)
        
        -- Disable weapon wheel
        DisableControlAction(0, 37, true)
        
        -- Disable phone (we have our own)
        DisableControlAction(0, 288, true)
        DisableControlAction(0, 289, true)
        
        -- Disable some other controls
        DisableControlAction(0, 166, true) -- F5 (character switch)
        DisableControlAction(0, 167, true) -- F6 (character switch)
        DisableControlAction(0, 168, true) -- F7 (character switch)
        DisableControlAction(0, 169, true) -- F8 (character switch)
        DisableControlAction(0, 56, true)  -- F9 (skip cutscene)
        DisableControlAction(0, 57, true)  -- F10 (skip cutscene)
    end
end)

-- Death system
local isDead = false

CreateThread(function()
    while true do
        Wait(1000)
        
        local playerPed = PlayerPedId()
        local health = GetEntityHealth(playerPed)
        
        if health <= 0 and not isDead then
            isDead = true
            TriggerEvent('core:playerDied')
            TriggerServerEvent('core:playerDied')
        elseif health > 0 and isDead then
            isDead = false
            TriggerEvent('core:playerRevived')
            TriggerServerEvent('core:playerRevived')
        end
    end
end)

-- Exports
exports('GetPlayerData', GetPlayerData)
exports('IsPlayerLoggedIn', IsPlayerLoggedIn)
exports('GetMoney', GetMoney)
exports('ShowNotification', ShowNotification)
exports('SpawnPlayer', SpawnPlayer)
