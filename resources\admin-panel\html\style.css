/* Admin Panel CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    overflow: hidden;
    direction: rtl;
}

.admin-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: all 0.3s ease;
}

.admin-panel.hidden {
    opacity: 0;
    pointer-events: none;
}

.panel-container {
    width: 90%;
    max-width: 1200px;
    height: 90%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
}

.panel-header h1 {
    font-size: 24px;
    font-weight: 600;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Navigation */
.panel-nav {
    background: #2d2d2d;
    padding: 0 20px;
    display: flex;
    gap: 10px;
    border-bottom: 1px solid #444;
}

.nav-btn {
    background: none;
    border: none;
    color: #ccc;
    padding: 15px 20px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.nav-btn.active {
    background: #1a1a1a;
    color: #667eea;
    border-bottom: 2px solid #667eea;
}

/* Content */
.panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    position: relative;
}

.tab-content {
    display: none;
    height: 100%;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    color: #fff;
}

.content-header h2 {
    font-size: 20px;
    font-weight: 600;
}

.refresh-btn {
    background: #667eea;
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* Players Grid */
.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.player-card {
    background: #333;
    border-radius: 10px;
    padding: 20px;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.player-card:hover {
    background: #3d3d3d;
    border-color: #667eea;
    transform: translateY(-2px);
}

.player-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.player-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.player-details h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.player-details p {
    font-size: 12px;
    color: #ccc;
}

.player-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
}

.stat-label {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 14px;
    font-weight: bold;
    color: #667eea;
}

/* Action Buttons */
.action-btn {
    background: #667eea;
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    margin: 5px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.action-btn.primary {
    background: #007AFF;
}

.action-btn.primary:hover {
    background: #0056b3;
}

.action-btn.success {
    background: #4CAF50;
}

.action-btn.success:hover {
    background: #45a049;
}

.action-btn.warning {
    background: #FF9800;
}

.action-btn.warning:hover {
    background: #e68900;
}

.action-btn.danger {
    background: #F44336;
}

.action-btn.danger:hover {
    background: #d32f2f;
}

/* Vehicle Categories */
.vehicle-categories {
    display: grid;
    gap: 20px;
}

.vehicle-category {
    background: #333;
    border-radius: 10px;
    padding: 20px;
    color: #fff;
}

.category-header {
    font-size: 18px;
    margin-bottom: 15px;
    color: #667eea;
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.vehicle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    color: #fff;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.vehicle-btn:hover {
    background: #667eea;
    border-color: #667eea;
}

/* Server Controls */
.server-controls {
    display: grid;
    gap: 30px;
}

.control-group {
    background: #333;
    border-radius: 10px;
    padding: 20px;
    color: #fff;
}

.control-group h3 {
    margin-bottom: 15px;
    color: #667eea;
}

.control-group select,
.control-group input,
.control-group textarea {
    width: 100%;
    padding: 10px;
    background: #2d2d2d;
    border: 1px solid #555;
    border-radius: 8px;
    color: #fff;
    margin-bottom: 10px;
}

.time-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.quick-action {
    background: #333;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.quick-action:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.quick-action i {
    font-size: 30px;
    margin-bottom: 10px;
}

.quick-action h3 {
    color: #fff;
    margin-bottom: 5px;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: #2d2d2d;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow-y: auto;
}

.modal-header {
    background: #667eea;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    border-radius: 15px 15px 0 0;
}

.modal-body {
    padding: 20px;
    color: #fff;
}

.player-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

/* Logs */
.logs-container {
    background: #333;
    border-radius: 10px;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.log-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    color: #fff;
}

.log-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
}

.log-details {
    font-size: 14px;
    color: #ccc;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}
