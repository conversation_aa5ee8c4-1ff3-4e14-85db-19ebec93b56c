# Modern FiveM Server - سيرفر FiveM حديث

## نظرة عامة
سيرفر FiveM حديث ومتطور مع جميع الأنظمة الأساسية والمتقدمة، مصمم خصيصاً للمجتمع العربي.

## الميزات الرئيسية

### 🔧 الأنظمة الأساسية
- **نظام أساسي متطور** مع قاعدة بيانات MongoDB
- **نظام المستخدمين والشخصيات** مع حفظ تلقائي
- **نظام الاقتصاد المتقدم** مع بنوك وعملات رقمية
- **نظام الوظائف التفاعلي**
- **نظام المركبات المتطور**
- **نظام العقارات**

### 📱 نظام الجوال (الضغط على K)
- **واجهة جوال حديثة** تشبه الهواتف الذكية
- **تطبيقات متعددة**: الاتصالات، الرسائل، البنك، الكاميرا
- **نظام إشعارات متقدم**
- **تطبيق البنك** لإدارة الأموال والتحويلات

### 🛡️ لوحة الإدارة المتقدمة (F1)
- **واجهة إدارة حديثة** مع تصميم متطور
- **إدارة اللاعبين**: طرد، حظر، نقل، مراقبة
- **إدارة المركبات**: إنشاء، حذف، إصلاح
- **إدارة الخادم**: الطقس، الوقت، الإعلانات
- **نظام السجلات** الشامل
- **إجراءات سريعة** للمهام الشائعة

### 💰 نظام الاقتصاد
- **أنواع أموال متعددة**: نقد، بنك، عملة رقمية
- **نظام بنكي متطور** مع فوائد وحدود يومية
- **نظام الرواتب** التلقائي
- **نظام الضرائب**
- **العملات الرقمية** مع أسعار متغيرة

## متطلبات التشغيل

### 1. تحميل ملفات الخادم
قم بتحميل ملفات FiveM Server من:
```
https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/
```

### 2. استخراج الملفات
استخرج جميع الملفات في مجلد السيرفر

### 3. تثبيت MongoDB
- قم بتثبيت MongoDB أو استخدم MongoDB Atlas
- تأكد من صحة connection string في `server.cfg`

### 4. تحديث الإعدادات
قم بتحديث الإعدادات التالية في `server.cfg`:
```cfg
sv_licenseKey "your_license_key_here"
set steam_webApiKey "your_steam_web_api_key"
add_principal identifier.steam:YOUR_STEAM_ID group.admin
```

## تشغيل الخادم

### الطريقة الأولى: استخدام run.bat
```bash
run.bat
```

### الطريقة الثانية: يدوياً
```bash
FXServer.exe +exec server.cfg
```

## الموارد المتضمنة

### الموارد الأساسية
- `core-system` - النظام الأساسي وقاعدة البيانات
- `phone-system` - نظام الجوال (K)
- `admin-panel` - لوحة الإدارة (F1)
- `economy-system` - نظام الاقتصاد
- `hud-system` - واجهة المستخدم

### الموارد الإضافية (قيد التطوير)
- `character-system` - نظام الشخصيات
- `vehicle-system` - نظام المركبات
- `property-system` - نظام العقارات
- `job-system` - نظام الوظائف
- `inventory-system` - نظام الإنفنتري

## قاعدة البيانات

### Collections المطلوبة
- `users` - بيانات المستخدمين
- `characters` - الشخصيات
- `vehicles` - المركبات
- `properties` - العقارات
- `inventory` - الإنفنتري
- `jobs` - الوظائف
- `banking` - البنوك
- `logs` - السجلات
- `phone_contacts` - جهات اتصال الجوال
- `phone_messages` - رسائل الجوال
- `admin_logs` - سجلات الإدارة

## الأوامر الأساسية

### أوامر اللاعبين
- `K` - فتح الجوال
- `F1` - فتح لوحة الإدارة (للإداريين فقط)

### أوامر الإدارة
جميع أوامر الإدارة متاحة من خلال لوحة الإدارة (F1)

## الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من صحة MongoDB connection string
   - تأكد من تشغيل MongoDB

2. **لا يمكن فتح الجوال**
   - تأكد من تحميل core-system و phone-system
   - تحقق من console للأخطاء

3. **لوحة الإدارة لا تفتح**
   - تأكد من إضافة Steam ID في server.cfg
   - تحقق من صلاحيات الإدارة

### التحديثات المستقبلية
- نظام الشخصيات المتقدم
- نظام المركبات مع التخصيص
- نظام العقارات القابلة للشراء
- نظام الوظائف التفاعلي
- نظام الإنفنتري مع drag & drop
- نظام الأصوات المتقدم
- نظام الطقس الديناميكي

## الترخيص
هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المطور
تم تطوير هذا السيرفر بواسطة Augment Agent لتوفير تجربة FiveM حديثة ومتطورة للمجتمع العربي.

---

**ملاحظة**: تأكد من تحديث جميع الإعدادات قبل تشغيل الخادم لأول مرة.
