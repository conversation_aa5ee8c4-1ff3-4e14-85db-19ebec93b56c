Config = {}

-- Economy Settings
Config.StartingMoney = {
    cash = 5000,
    bank = 10000,
    crypto = 0
}

-- Money Types
Config.MoneyTypes = {
    cash = {
        label = "نقد",
        icon = "fas fa-money-bill-wave",
        color = "#4CAF50"
    },
    bank = {
        label = "بنك",
        icon = "fas fa-credit-card", 
        color = "#2196F3"
    },
    crypto = {
        label = "عملة رقمية",
        icon = "fab fa-bitcoin",
        color = "#FF9800"
    }
}

-- Banking Settings
Config.Banking = {
    InterestRate = 0.02, -- 2% daily interest
    TransferFee = 50, -- Fee for transfers
    WithdrawLimit = 50000, -- Daily withdraw limit
    DepositLimit = 100000, -- Daily deposit limit
    MinBalance = 100 -- Minimum balance to keep account active
}

-- ATM Locations
Config.ATMLocations = {
    {x = 147.4, y = -1035.8, z = 29.3},
    {x = 145.9, y = -1035.2, z = 29.3},
    {x = 112.4, y = -819.0, z = 31.3},
    {x = 112.9, y = -818.7, z = 31.3},
    {x = 119.9, y = -883.8, z = 31.1},
    {x = 149.4, y = -1037.7, z = 29.4},
    {x = 527.3, y = -160.7, z = 57.1},
    {x = 285.2, y = 143.5, z = 104.2},
    {x = 157.7, y = 233.5, z = 106.6},
    {x = -164.6, y = 233.0, z = 94.9},
    {x = -1827.0, y = 785.0, z = 138.0},
    {x = -2975.0, y = 380.1, z = 15.0},
    {x = -3144.4, y = 1127.8, z = 20.9},
    {x = -1305.4, y = -706.2, z = 25.3},
    {x = -717.6, y = -915.9, z = 19.2},
    {x = -526.6, y = -1222.9, z = 18.5},
    {x = -256.2, y = -719.6, z = 33.5},
    {x = -203.5, y = -861.0, z = 30.3},
    {x = 170.0, y = 6642.9, z = 31.6}
}

-- Bank Locations
Config.BankLocations = {
    {
        name = "Fleeca Bank",
        coords = {x = 147.04, y = -1035.8, z = 29.34},
        blip = {sprite = 108, color = 2, scale = 0.8}
    },
    {
        name = "Fleeca Bank",
        coords = {x = -2962.71, y = 483.0, z = 15.7},
        blip = {sprite = 108, color = 2, scale = 0.8}
    },
    {
        name = "Fleeca Bank",
        coords = {x = -112.2, y = 6469.3, z = 31.6},
        blip = {sprite = 108, color = 2, scale = 0.8}
    },
    {
        name = "Fleeca Bank",
        coords = {x = 314.2, y = -278.6, z = 54.2},
        blip = {sprite = 108, color = 2, scale = 0.8}
    },
    {
        name = "Fleeca Bank",
        coords = {x = -351.5, y = -49.5, z = 49.0},
        blip = {sprite = 108, color = 2, scale = 0.8}
    },
    {
        name = "Fleeca Bank",
        coords = {x = 1175.0, y = 2706.6, z = 38.1},
        blip = {sprite = 108, color = 2, scale = 0.8}
    }
}

-- Crypto Settings
Config.Crypto = {
    Types = {
        bitcoin = {
            name = "Bitcoin",
            symbol = "BTC",
            icon = "fab fa-bitcoin",
            color = "#F7931A",
            volatility = 0.05 -- 5% price change
        },
        ethereum = {
            name = "Ethereum", 
            symbol = "ETH",
            icon = "fab fa-ethereum",
            color = "#627EEA",
            volatility = 0.08 -- 8% price change
        },
        litecoin = {
            name = "Litecoin",
            symbol = "LTC", 
            icon = "fas fa-coins",
            color = "#BFBBBB",
            volatility = 0.10 -- 10% price change
        }
    },
    UpdateInterval = 300000, -- 5 minutes
    StartingPrices = {
        bitcoin = 45000,
        ethereum = 3000,
        litecoin = 150
    }
}

-- Job Salaries (per hour)
Config.JobSalaries = {
    unemployed = 0,
    police = 500,
    ambulance = 450,
    mechanic = 400,
    taxi = 300,
    realestate = 350,
    lawyer = 600,
    judge = 800,
    mayor = 1000
}

-- Business Settings
Config.Businesses = {
    {
        name = "24/7 Store",
        type = "shop",
        coords = {x = 25.7, y = -1347.3, z = 29.5},
        price = 50000,
        income = 1000 -- per hour
    },
    {
        name = "Gas Station",
        type = "fuel",
        coords = {x = 49.4, y = -1757.5, z = 29.4},
        price = 75000,
        income = 1500
    },
    {
        name = "Car Dealership",
        type = "vehicles",
        coords = {x = -56.7, y = -1096.6, z = 26.4},
        price = 200000,
        income = 3000
    }
}

-- Tax Settings
Config.Taxes = {
    IncomeTax = 0.15, -- 15% income tax
    SalesTax = 0.08, -- 8% sales tax
    PropertyTax = 0.02 -- 2% property tax (monthly)
}
