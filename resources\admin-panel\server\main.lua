-- Admin Panel Server
local adminPlayers = {}
local bannedPlayers = {}

-- Check if player is admin
function IsPlayerAdmin(source)
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    if not identifier then return false end
    
    for _, group in ipairs(Config.AdminGroups) do
        if IsPlayerAceAllowed(source, "group." .. group) then
            return true
        end
    end
    
    return false
end

-- Get player permissions
function GetPlayerPermissions(source)
    local permissions = {}
    
    for group, perms in pairs(Config.Permissions) do
        if IsPlayerAceAllowed(source, "group." .. group) then
            for _, perm in ipairs(perms) do
                if perm == "all" then
                    -- Grant all permissions
                    for _, allPerms in pairs(Config.Permissions) do
                        for _, p in ipairs(allPerms) do
                            if p ~= "all" then
                                permissions[p] = true
                            end
                        end
                    end
                    break
                else
                    permissions[perm] = true
                end
            end
        end
    end
    
    return permissions
end

-- Check admin permissions
RegisterServerEvent('admin:checkPermissions')
AddEventHandler('admin:checkPermissions', function()
    local source = source
    local isAdmin = IsPlayerAdmin(source)
    local permissions = {}
    
    if isAdmin then
        permissions = GetPlayerPermissions(source)
        adminPlayers[source] = {
            permissions = permissions,
            identifier = exports['core-system']:GetPlayerIdentifier(source)
        }
    end
    
    TriggerClientEvent('admin:receivePermissions', source, permissions, isAdmin)
end)

-- Get online players
RegisterServerEvent('admin:getOnlinePlayers')
AddEventHandler('admin:getOnlinePlayers', function()
    local source = source
    if not IsPlayerAdmin(source) then return end
    
    local players = {}
    for _, playerId in ipairs(GetPlayers()) do
        local playerData = exports['core-system']:GetPlayerData(playerId)
        if playerData then
            table.insert(players, {
                id = playerId,
                name = GetPlayerName(playerId),
                identifier = exports['core-system']:GetPlayerIdentifier(playerId),
                money = playerData.money or 0,
                bank = playerData.bank or 0,
                job = playerData.job or "unemployed",
                ping = GetPlayerPing(playerId)
            })
        end
    end
    
    TriggerClientEvent('admin:receiveOnlinePlayers', source, players)
end)

-- Kick player
RegisterServerEvent('admin:kickPlayer')
AddEventHandler('admin:kickPlayer', function(targetId, reason)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.kick") then return end
    
    local targetName = GetPlayerName(targetId)
    if targetName then
        DropPlayer(targetId, "تم طردك من الخادم: " .. (reason or "بدون سبب"))
        
        -- Log action
        LogAdminAction(source, "kick", "Kicked " .. targetName .. " (ID: " .. targetId .. ") - Reason: " .. (reason or "No reason"))
        
        -- Notify all admins
        NotifyAdmins(GetPlayerName(source) .. " طرد اللاعب " .. targetName .. " - السبب: " .. (reason or "بدون سبب"))
    end
end)

-- Ban player
RegisterServerEvent('admin:banPlayer')
AddEventHandler('admin:banPlayer', function(targetId, reason, duration)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.ban") then return end
    
    local targetIdentifier = exports['core-system']:GetPlayerIdentifier(targetId)
    local targetName = GetPlayerName(targetId)
    
    if targetIdentifier and targetName then
        local banData = {
            identifier = targetIdentifier,
            name = targetName,
            reason = reason or "بدون سبب",
            duration = duration or 0, -- 0 = permanent
            banned_by = exports['core-system']:GetPlayerIdentifier(source),
            banned_at = os.time(),
            expires_at = duration and (os.time() + (duration * 3600)) or 0
        }
        
        -- Save ban to database
        exports['mongodb-async']:insertOne("bans", banData, function(success)
            if success then
                bannedPlayers[targetIdentifier] = banData
                
                -- Kick player
                DropPlayer(targetId, "تم حظرك من الخادم: " .. banData.reason)
                
                -- Log action
                LogAdminAction(source, "ban", "Banned " .. targetName .. " (ID: " .. targetId .. ") - Reason: " .. banData.reason)
                
                -- Notify all admins
                local durationText = duration and (duration .. " ساعة") or "دائم"
                NotifyAdmins(GetPlayerName(source) .. " حظر اللاعب " .. targetName .. " لمدة " .. durationText .. " - السبب: " .. banData.reason)
            end
        end)
    end
end)

-- Teleport to player
RegisterServerEvent('admin:teleportToPlayer')
AddEventHandler('admin:teleportToPlayer', function(targetId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.teleport") then return end
    
    local targetPed = GetPlayerPed(targetId)
    if targetPed then
        local coords = GetEntityCoords(targetPed)
        TriggerClientEvent('admin:teleportPlayer', source, coords)
        
        LogAdminAction(source, "teleport", "Teleported to " .. GetPlayerName(targetId))
    end
end)

-- Teleport player to me
RegisterServerEvent('admin:teleportPlayerToMe')
AddEventHandler('admin:teleportPlayerToMe', function(targetId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.teleport") then return end
    
    local adminPed = GetPlayerPed(source)
    if adminPed then
        local coords = GetEntityCoords(adminPed)
        TriggerClientEvent('admin:teleportPlayer', targetId, coords)
        
        LogAdminAction(source, "teleport", "Teleported " .. GetPlayerName(targetId) .. " to admin")
    end
end)

-- Spectate player
RegisterServerEvent('admin:spectatePlayer')
AddEventHandler('admin:spectatePlayer', function(targetId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.spectate") then return end
    
    TriggerClientEvent('admin:startSpectate', source, targetId)
    LogAdminAction(source, "spectate", "Started spectating " .. GetPlayerName(targetId))
end)

-- Freeze player
RegisterServerEvent('admin:freezePlayer')
AddEventHandler('admin:freezePlayer', function(targetId, freeze)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.freeze") then return end
    
    TriggerClientEvent('admin:freezePlayer', targetId, freeze)
    
    local action = freeze and "frozen" or "unfrozen"
    LogAdminAction(source, "freeze", "Player " .. GetPlayerName(targetId) .. " " .. action)
end)

-- Heal player
RegisterServerEvent('admin:healPlayer')
AddEventHandler('admin:healPlayer', function(targetId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.heal") then return end
    
    TriggerClientEvent('admin:healPlayer', targetId)
    LogAdminAction(source, "heal", "Healed " .. GetPlayerName(targetId))
end)

-- Revive player
RegisterServerEvent('admin:revivePlayer')
AddEventHandler('admin:revivePlayer', function(targetId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.revive") then return end
    
    TriggerClientEvent('admin:revivePlayer', targetId)
    LogAdminAction(source, "revive", "Revived " .. GetPlayerName(targetId))
end)

-- Set player job
RegisterServerEvent('admin:setPlayerJob')
AddEventHandler('admin:setPlayerJob', function(targetId, job, grade)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.setjob") then return end
    
    local playerData = exports['core-system']:GetPlayerData(targetId)
    if playerData then
        playerData.job = job
        playerData.job_grade = grade or 0
        exports['core-system']:SavePlayerData(targetId, playerData)
        
        TriggerClientEvent('core:updateJob', targetId, job, grade)
        LogAdminAction(source, "setjob", "Set " .. GetPlayerName(targetId) .. " job to " .. job .. " grade " .. (grade or 0))
    end
end)

-- Give player money
RegisterServerEvent('admin:givePlayerMoney')
AddEventHandler('admin:givePlayerMoney', function(targetId, moneyType, amount)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.givemoney") then return end
    
    if exports['core-system']:AddPlayerMoney(targetId, moneyType, amount) then
        LogAdminAction(source, "givemoney", "Gave " .. GetPlayerName(targetId) .. " $" .. amount .. " " .. moneyType)
    end
end)

-- Remove player money
RegisterServerEvent('admin:removePlayerMoney')
AddEventHandler('admin:removePlayerMoney', function(targetId, moneyType, amount)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "player.removemoney") then return end
    
    if exports['core-system']:RemovePlayerMoney(targetId, moneyType, amount) then
        LogAdminAction(source, "removemoney", "Removed $" .. amount .. " " .. moneyType .. " from " .. GetPlayerName(targetId))
    end
end)

-- Spawn vehicle
RegisterServerEvent('admin:spawnVehicle')
AddEventHandler('admin:spawnVehicle', function(model)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "vehicle.spawn") then return end
    
    TriggerClientEvent('admin:spawnVehicle', source, model)
    LogAdminAction(source, "spawnvehicle", "Spawned vehicle: " .. model)
end)

-- Delete vehicle
RegisterServerEvent('admin:deleteVehicle')
AddEventHandler('admin:deleteVehicle', function(networkId)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "vehicle.delete") then return end
    
    local vehicle = NetworkGetEntityFromNetworkId(networkId)
    if DoesEntityExist(vehicle) then
        DeleteEntity(vehicle)
        LogAdminAction(source, "deletevehicle", "Deleted vehicle")
    end
end)

-- Set weather
RegisterServerEvent('admin:setWeather')
AddEventHandler('admin:setWeather', function(weather)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "server.weather") then return end
    
    TriggerClientEvent('admin:setWeather', -1, weather)
    LogAdminAction(source, "weather", "Changed weather to " .. weather)
end)

-- Set time
RegisterServerEvent('admin:setTime')
AddEventHandler('admin:setTime', function(hour, minute)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "server.time") then return end
    
    TriggerClientEvent('admin:setTime', -1, hour, minute)
    LogAdminAction(source, "time", "Changed time to " .. hour .. ":" .. minute)
end)

-- Send announcement
RegisterServerEvent('admin:sendAnnouncement')
AddEventHandler('admin:sendAnnouncement', function(message)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "server.announce") then return end
    
    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 0, 0},
        multiline = true,
        args = {"[إعلان]", message}
    })
    
    LogAdminAction(source, "announcement", "Sent announcement: " .. message)
end)

-- Execute quick action
RegisterServerEvent('admin:executeQuickAction')
AddEventHandler('admin:executeQuickAction', function(action)
    local source = source
    if not IsPlayerAdmin(source) then return end
    
    if action == "heal_all" and HasPermission(source, "player.heal") then
        TriggerClientEvent('admin:healPlayer', -1)
        LogAdminAction(source, "heal_all", "Healed all players")
        NotifyAdmins(GetPlayerName(source) .. " شفى جميع اللاعبين")
        
    elseif action == "revive_all" and HasPermission(source, "player.revive") then
        TriggerClientEvent('admin:revivePlayer', -1)
        LogAdminAction(source, "revive_all", "Revived all players")
        NotifyAdmins(GetPlayerName(source) .. " أحيا جميع اللاعبين")
        
    elseif action == "clear_chat" and HasPermission(source, "server.announce") then
        TriggerClientEvent('chat:clear', -1)
        LogAdminAction(source, "clear_chat", "Cleared chat")
        
    elseif action == "restart_warning" and HasPermission(source, "server.announce") then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 165, 0},
            multiline = true,
            args = {"[تحذير]", "سيتم إعادة تشغيل الخادم خلال 5 دقائق!"}
        })
        LogAdminAction(source, "restart_warning", "Sent restart warning")
    end
end)

-- Get logs
RegisterServerEvent('admin:getLogs')
AddEventHandler('admin:getLogs', function(logType, limit)
    local source = source
    if not IsPlayerAdmin(source) or not HasPermission(source, "logs.view") then return end
    
    local query = logType and {type = logType} or {}
    exports['mongodb-async']:find("admin_logs", query, {limit = limit or 50, sort = {timestamp = -1}}, function(success, logs)
        if success then
            TriggerClientEvent('admin:receiveLogs', source, logs or {})
        end
    end)
end)

-- Helper functions
function HasPermission(source, permission)
    local adminData = adminPlayers[source]
    return adminData and (adminData.permissions[permission] or adminData.permissions["all"])
end

function LogAdminAction(source, action, details)
    local logData = {
        admin_identifier = exports['core-system']:GetPlayerIdentifier(source),
        admin_name = GetPlayerName(source),
        action = action,
        details = details,
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    exports['mongodb-async']:insertOne("admin_logs", logData)
end

function NotifyAdmins(message)
    for playerId, adminData in pairs(adminPlayers) do
        if GetPlayerName(playerId) then
            TriggerClientEvent('chat:addMessage', playerId, {
                color = {255, 165, 0},
                multiline = true,
                args = {"[إدارة]", message}
            })
        end
    end
end

-- Load banned players on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        exports['mongodb-async']:find("bans", {}, function(success, bans)
            if success and bans then
                for _, ban in ipairs(bans) do
                    if ban.expires_at == 0 or ban.expires_at > os.time() then
                        bannedPlayers[ban.identifier] = ban
                    end
                end
                print("^2[ADMIN] Loaded " .. #bans .. " bans^7")
            end
        end)
    end
end)

-- Check for banned players on connect
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    if identifier and bannedPlayers[identifier] then
        local ban = bannedPlayers[identifier]
        if ban.expires_at == 0 or ban.expires_at > os.time() then
            local reason = "أنت محظور من الخادم\nالسبب: " .. ban.reason
            if ban.expires_at > 0 then
                local timeLeft = ban.expires_at - os.time()
                local hoursLeft = math.ceil(timeLeft / 3600)
                reason = reason .. "\nينتهي الحظر خلال: " .. hoursLeft .. " ساعة"
            else
                reason = reason .. "\nالحظر: دائم"
            end
            setKickReason(reason)
        else
            -- Ban expired, remove it
            bannedPlayers[identifier] = nil
            exports['mongodb-async']:deleteOne("bans", {identifier = identifier})
        end
    end
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function()
    local source = source
    adminPlayers[source] = nil
end)

-- Exports
exports('IsPlayerAdmin', IsPlayerAdmin)
exports('GetPlayerPermissions', GetPlayerPermissions)
exports('LogAdminAction', LogAdminAction)
