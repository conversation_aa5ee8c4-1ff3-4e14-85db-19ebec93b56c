// Admin Panel JavaScript
let adminData = {
    permissions: {},
    players: [],
    config: {},
    selectedPlayer: null
};

let currentTab = 'players';

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.getAttribute('data-tab');
            switchTab(tab);
        });
    });
    
    // Close panel with ESC key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeAdminPanel();
        }
    });
}

// Switch tabs
function switchTab(tabName) {
    if (tabName === currentTab) return;
    
    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    currentTab = tabName;
    
    // Load tab data
    loadTabData(tabName);
}

// Load tab data
function loadTabData(tabName) {
    switch(tabName) {
        case 'players':
            refreshPlayers();
            break;
        case 'vehicles':
            loadVehicleCategories();
            break;
        case 'server':
            loadServerControls();
            break;
        case 'logs':
            loadLogs();
            break;
        case 'quick':
            loadQuickActions();
            break;
    }
}

// NUI Message handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openAdminPanel':
            openAdminPanel(data);
            break;
        case 'closeAdminPanel':
            closeAdminPanel();
            break;
        case 'updatePlayers':
            updatePlayers(data.players);
            break;
        case 'updateLogs':
            updateLogs(data.logs);
            break;
    }
});

// Open admin panel
function openAdminPanel(data) {
    adminData.permissions = data.permissions;
    adminData.config = data.config;
    
    document.getElementById('admin-panel').classList.remove('hidden');
    
    // Load initial data
    loadTabData(currentTab);
}

// Close admin panel
function closeAdminPanel() {
    document.getElementById('admin-panel').classList.add('hidden');
    
    // Send close message to FiveM
    fetch(`https://${GetParentResourceName()}/closeAdminPanel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Refresh players
function refreshPlayers() {
    fetch(`https://${GetParentResourceName()}/getOnlinePlayers`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Update players display
function updatePlayers(players) {
    adminData.players = players;
    const playersGrid = document.getElementById('players-grid');
    playersGrid.innerHTML = '';
    
    if (players.length === 0) {
        playersGrid.innerHTML = '<div class="no-data">لا يوجد لاعبين متصلين</div>';
        return;
    }
    
    players.forEach(player => {
        const playerCard = createPlayerCard(player);
        playersGrid.appendChild(playerCard);
    });
}

// Create player card
function createPlayerCard(player) {
    const card = document.createElement('div');
    card.className = 'player-card';
    card.onclick = () => openPlayerModal(player);
    
    card.innerHTML = `
        <div class="player-header">
            <div class="player-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="player-details">
                <h3>${player.name}</h3>
                <p>ID: ${player.id} | Ping: ${player.ping}ms</p>
            </div>
        </div>
        <div class="player-stats">
            <div class="stat-item">
                <div class="stat-label">نقد</div>
                <div class="stat-value">$${player.money.toLocaleString()}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">بنك</div>
                <div class="stat-value">$${player.bank.toLocaleString()}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">وظيفة</div>
                <div class="stat-value">${player.job}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">معرف</div>
                <div class="stat-value">${player.identifier.substring(0, 10)}...</div>
            </div>
        </div>
    `;
    
    return card;
}

// Open player modal
function openPlayerModal(player) {
    adminData.selectedPlayer = player;
    
    document.getElementById('modal-player-name').textContent = player.name;
    document.getElementById('player-info').innerHTML = `
        <div class="info-grid">
            <div class="info-item">
                <strong>ID:</strong> ${player.id}
            </div>
            <div class="info-item">
                <strong>الاسم:</strong> ${player.name}
            </div>
            <div class="info-item">
                <strong>المعرف:</strong> ${player.identifier}
            </div>
            <div class="info-item">
                <strong>النقد:</strong> $${player.money.toLocaleString()}
            </div>
            <div class="info-item">
                <strong>البنك:</strong> $${player.bank.toLocaleString()}
            </div>
            <div class="info-item">
                <strong>الوظيفة:</strong> ${player.job}
            </div>
            <div class="info-item">
                <strong>البينغ:</strong> ${player.ping}ms
            </div>
        </div>
    `;
    
    document.getElementById('player-modal').classList.remove('hidden');
}

// Close player modal
function closePlayerModal() {
    document.getElementById('player-modal').classList.add('hidden');
    adminData.selectedPlayer = null;
}

// Player actions
function kickPlayer() {
    if (!adminData.selectedPlayer) return;
    
    const reason = prompt('سبب الطرد:');
    if (reason !== null) {
        fetch(`https://${GetParentResourceName()}/kickPlayer`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                playerId: adminData.selectedPlayer.id,
                reason: reason
            })
        });
        closePlayerModal();
    }
}

function banPlayer() {
    if (!adminData.selectedPlayer) return;
    
    const reason = prompt('سبب الحظر:');
    if (reason !== null) {
        const duration = prompt('مدة الحظر بالساعات (0 للحظر الدائم):');
        if (duration !== null) {
            fetch(`https://${GetParentResourceName()}/banPlayer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playerId: adminData.selectedPlayer.id,
                    reason: reason,
                    duration: parseInt(duration) || 0
                })
            });
            closePlayerModal();
        }
    }
}

function teleportToPlayer() {
    if (!adminData.selectedPlayer) return;
    
    fetch(`https://${GetParentResourceName()}/teleportToPlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: adminData.selectedPlayer.id
        })
    });
    closePlayerModal();
}

function teleportPlayerToMe() {
    if (!adminData.selectedPlayer) return;
    
    fetch(`https://${GetParentResourceName()}/teleportPlayerToMe`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: adminData.selectedPlayer.id
        })
    });
    closePlayerModal();
}

function spectatePlayer() {
    if (!adminData.selectedPlayer) return;
    
    fetch(`https://${GetParentResourceName()}/spectatePlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: adminData.selectedPlayer.id
        })
    });
    closePlayerModal();
}

function healPlayer() {
    if (!adminData.selectedPlayer) return;
    
    fetch(`https://${GetParentResourceName()}/healPlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: adminData.selectedPlayer.id
        })
    });
}

function revivePlayer() {
    if (!adminData.selectedPlayer) return;
    
    fetch(`https://${GetParentResourceName()}/revivePlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: adminData.selectedPlayer.id
        })
    });
}

// Load vehicle categories
function loadVehicleCategories() {
    const container = document.getElementById('vehicle-categories');
    container.innerHTML = '';
    
    if (adminData.config.vehicleCategories) {
        adminData.config.vehicleCategories.forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'vehicle-category';
            
            categoryDiv.innerHTML = `
                <div class="category-header">${category.label}</div>
                <div class="vehicles-grid">
                    ${category.vehicles.map(vehicle => 
                        `<button class="vehicle-btn" onclick="spawnVehicle('${vehicle.model}')">
                            ${vehicle.name}
                        </button>`
                    ).join('')}
                </div>
            `;
            
            container.appendChild(categoryDiv);
        });
    }
}

// Spawn vehicle
function spawnVehicle(model) {
    fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: model
        })
    });
}

// Delete current vehicle
function deleteCurrentVehicle() {
    fetch(`https://${GetParentResourceName()}/deleteVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Repair current vehicle
function repairCurrentVehicle() {
    fetch(`https://${GetParentResourceName()}/repairVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Load server controls
function loadServerControls() {
    const weatherSelect = document.getElementById('weather-select');
    weatherSelect.innerHTML = '';
    
    if (adminData.config.weatherTypes) {
        adminData.config.weatherTypes.forEach(weather => {
            const option = document.createElement('option');
            option.value = weather.name;
            option.textContent = weather.label;
            weatherSelect.appendChild(option);
        });
    }
}

// Set weather
function setWeather() {
    const weather = document.getElementById('weather-select').value;
    if (weather) {
        fetch(`https://${GetParentResourceName()}/setWeather`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                weather: weather
            })
        });
    }
}

// Set time
function setTime() {
    const hour = document.getElementById('hour-input').value;
    const minute = document.getElementById('minute-input').value;
    
    if (hour !== '' && minute !== '') {
        fetch(`https://${GetParentResourceName()}/setTime`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                hour: parseInt(hour),
                minute: parseInt(minute)
            })
        });
    }
}

// Send announcement
function sendAnnouncement() {
    const message = document.getElementById('announcement-text').value;
    if (message.trim()) {
        fetch(`https://${GetParentResourceName()}/sendAnnouncement`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message
            })
        });
        document.getElementById('announcement-text').value = '';
    }
}

// Load quick actions
function loadQuickActions() {
    const container = document.getElementById('quick-actions');
    container.innerHTML = '';
    
    if (adminData.config.quickActions) {
        adminData.config.quickActions.forEach(action => {
            const actionDiv = document.createElement('div');
            actionDiv.className = 'quick-action';
            actionDiv.style.color = action.color;
            actionDiv.onclick = () => executeQuickAction(action.name);
            
            actionDiv.innerHTML = `
                <i class="${action.icon}"></i>
                <h3>${action.label}</h3>
            `;
            
            container.appendChild(actionDiv);
        });
    }
}

// Execute quick action
function executeQuickAction(action) {
    fetch(`https://${GetParentResourceName()}/executeQuickAction`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action
        })
    });
}

// Load logs
function loadLogs() {
    const logType = document.getElementById('log-type-select').value;
    
    fetch(`https://${GetParentResourceName()}/getLogs`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            logType: logType,
            limit: 50
        })
    });
}

// Update logs display
function updateLogs(logs) {
    const container = document.getElementById('logs-container');
    container.innerHTML = '';
    
    if (logs.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد سجلات</div>';
        return;
    }
    
    logs.forEach(log => {
        const logDiv = document.createElement('div');
        logDiv.className = 'log-item';
        
        logDiv.innerHTML = `
            <div class="log-header">
                <span>${log.admin_name}</span>
                <span>${log.date}</span>
            </div>
            <div class="log-details">
                <strong>${log.action}:</strong> ${log.details}
            </div>
        `;
        
        container.appendChild(logDiv);
    });
}

// Get parent resource name for FiveM
function GetParentResourceName() {
    return window.location.hostname;
}
