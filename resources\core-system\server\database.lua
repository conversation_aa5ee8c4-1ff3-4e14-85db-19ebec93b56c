-- MongoDB Database Handler
local MongoDB = {}

-- Initialize MongoDB Connection
function MongoDB.Init()
    local connectionString = GetConvar("mongodb_connection_string", Config.MongoDB.ConnectionString)
    local database = GetConvar("mongodb_database", Config.MongoDB.Database)
    
    exports['mongodb-async']:connect(connectionString, database, function(success)
        if success then
            print("^2[CORE] MongoDB connected successfully!^7")
            MongoDB.CreateCollections()
        else
            print("^1[CORE] Failed to connect to MongoDB!^7")
        end
    end)
end

-- Create necessary collections
function MongoDB.CreateCollections()
    local collections = {
        "users",
        "characters", 
        "vehicles",
        "properties",
        "inventory",
        "jobs",
        "banking",
        "logs",
        "phone_contacts",
        "phone_messages",
        "admin_logs"
    }
    
    for _, collection in ipairs(collections) do
        exports['mongodb-async']:createCollection(collection, function(success)
            if success then
                print("^2[CORE] Collection '" .. collection .. "' created/verified^7")
            end
        end)
    end
end

-- User Functions
function MongoDB.CreateUser(identifier, data, callback)
    local userData = {
        identifier = identifier,
        name = data.name or "Unknown",
        money = data.money or Config.DefaultMoney,
        bank = data.bank or Config.DefaultBank,
        job = data.job or "unemployed",
        job_grade = data.job_grade or 0,
        position = data.position or Config.DefaultSpawn,
        inventory = data.inventory or {},
        metadata = data.metadata or {},
        created_at = os.time(),
        last_login = os.time()
    }
    
    exports['mongodb-async']:insertOne("users", userData, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

function MongoDB.GetUser(identifier, callback)
    exports['mongodb-async']:findOne("users", {identifier = identifier}, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

function MongoDB.UpdateUser(identifier, data, callback)
    data.last_login = os.time()
    
    exports['mongodb-async']:updateOne("users", {identifier = identifier}, {["$set"] = data}, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

-- Character Functions
function MongoDB.CreateCharacter(identifier, charData, callback)
    local characterData = {
        identifier = identifier,
        firstname = charData.firstname,
        lastname = charData.lastname,
        dateofbirth = charData.dateofbirth,
        sex = charData.sex,
        height = charData.height,
        skin = charData.skin or {},
        position = charData.position or Config.DefaultSpawn,
        created_at = os.time()
    }
    
    exports['mongodb-async']:insertOne("characters", characterData, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

-- Vehicle Functions
function MongoDB.SaveVehicle(vehicleData, callback)
    exports['mongodb-async']:insertOne("vehicles", vehicleData, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

function MongoDB.GetPlayerVehicles(identifier, callback)
    exports['mongodb-async']:find("vehicles", {owner = identifier}, function(success, result)
        if callback then
            callback(success, result)
        end
    end)
end

-- Logging Function
function MongoDB.Log(logType, message, playerData)
    local logData = {
        type = logType,
        message = message,
        player = playerData or {},
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    exports['mongodb-async']:insertOne("logs", logData, function(success)
        if not success then
            print("^1[CORE] Failed to save log: " .. message .. "^7")
        end
    end)
end

-- Export functions
exports('ExecuteQuery', function(collection, query, callback)
    exports['mongodb-async']:find(collection, query, callback)
end)

exports('CreateUser', MongoDB.CreateUser)
exports('GetUser', MongoDB.GetUser)
exports('UpdateUser', MongoDB.UpdateUser)
exports('CreateCharacter', MongoDB.CreateCharacter)
exports('SaveVehicle', MongoDB.SaveVehicle)
exports('GetPlayerVehicles', MongoDB.GetPlayerVehicles)
exports('Log', MongoDB.Log)

-- Initialize on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        MongoDB.Init()
    end
end)
