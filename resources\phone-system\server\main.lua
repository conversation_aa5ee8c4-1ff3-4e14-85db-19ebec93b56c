-- Phone System Server
local phoneNumbers = {}
local activeCalls = {}

-- Generate phone number
function GeneratePhoneNumber()
    local number = ""
    for i = 1, 8 do
        number = number .. math.random(0, 9)
    end
    return "05" .. number
end

-- Get or create phone number for player
RegisterServerEvent('phone:getPhoneNumber')
AddEventHandler('phone:getPhoneNumber', function()
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    if phoneNumbers[identifier] then
        TriggerClientEvent('phone:receivePhoneNumber', source, phoneNumbers[identifier])
    else
        local newNumber = GeneratePhoneNumber()
        phoneNumbers[identifier] = newNumber
        
        -- Save to database
        exports['core-system']:UpdateUser(identifier, {phone_number = newNumber})
        
        TriggerClientEvent('phone:receivePhoneNumber', source, newNumber)
    end
end)

-- Make call
RegisterServerEvent('phone:makeCall')
AddEventHandler('phone:makeCall', function(targetNumber)
    local source = source
    local callerIdentifier = exports['core-system']:GetPlayerIdentifier(source)
    local callerNumber = phoneNumbers[callerIdentifier]
    local callerName = GetPlayerName(source)
    
    -- Find target player
    local targetSource = nil
    for identifier, number in pairs(phoneNumbers) do
        if number == targetNumber then
            -- Find player by identifier
            for _, playerId in ipairs(GetPlayers()) do
                if exports['core-system']:GetPlayerIdentifier(playerId) == identifier then
                    targetSource = playerId
                    break
                end
            end
            break
        end
    end
    
    if targetSource then
        -- Send incoming call to target
        TriggerClientEvent('phone:incomingCall', targetSource, callerNumber, callerName)
        
        -- Log the call
        exports['core-system']:Log("phone_call", callerName .. " called " .. targetNumber, {
            caller = callerIdentifier,
            target = targetNumber,
            caller_name = callerName
        })
    else
        TriggerClientEvent('phone:callFailed', source, "الرقم غير متاح")
    end
end)

-- Send message
RegisterServerEvent('phone:sendMessage')
AddEventHandler('phone:sendMessage', function(targetNumber, message)
    local source = source
    local senderIdentifier = exports['core-system']:GetPlayerIdentifier(source)
    local senderNumber = phoneNumbers[senderIdentifier]
    local senderName = GetPlayerName(source)
    
    -- Find target player
    local targetSource = nil
    local targetIdentifier = nil
    for identifier, number in pairs(phoneNumbers) do
        if number == targetNumber then
            targetIdentifier = identifier
            -- Find player by identifier
            for _, playerId in ipairs(GetPlayers()) do
                if exports['core-system']:GetPlayerIdentifier(playerId) == identifier then
                    targetSource = playerId
                    break
                end
            end
            break
        end
    end
    
    if targetIdentifier then
        -- Save message to database
        local messageData = {
            sender = senderIdentifier,
            receiver = targetIdentifier,
            sender_number = senderNumber,
            receiver_number = targetNumber,
            message = message,
            timestamp = os.time(),
            read = false
        }
        
        exports['core-system']:ExecuteQuery("phone_messages", {}, function(success)
            if success then
                -- Insert message
                exports['mongodb-async']:insertOne("phone_messages", messageData, function(insertSuccess)
                    if insertSuccess and targetSource then
                        TriggerClientEvent('phone:receiveMessage', targetSource, senderNumber, message, senderName)
                    end
                end)
            end
        end)
        
        -- Log the message
        exports['core-system']:Log("phone_message", senderName .. " sent message to " .. targetNumber, {
            sender = senderIdentifier,
            target = targetNumber,
            message = message
        })
    else
        TriggerClientEvent('phone:messageFailed', source, "الرقم غير متاح")
    end
end)

-- Add contact
RegisterServerEvent('phone:addContact')
AddEventHandler('phone:addContact', function(name, number)
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    local contactData = {
        owner = identifier,
        name = name,
        number = number,
        created_at = os.time()
    }
    
    exports['mongodb-async']:insertOne("phone_contacts", contactData, function(success)
        if success then
            TriggerClientEvent('phone:contactAdded', source, "تم إضافة جهة الاتصال بنجاح")
            -- Send updated contacts
            GetPlayerContacts(source)
        else
            TriggerClientEvent('phone:contactFailed', source, "فشل في إضافة جهة الاتصال")
        end
    end)
end)

-- Delete contact
RegisterServerEvent('phone:deleteContact')
AddEventHandler('phone:deleteContact', function(contactId)
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    exports['mongodb-async']:deleteOne("phone_contacts", {_id = contactId, owner = identifier}, function(success)
        if success then
            TriggerClientEvent('phone:contactDeleted', source, "تم حذف جهة الاتصال")
            -- Send updated contacts
            GetPlayerContacts(source)
        else
            TriggerClientEvent('phone:contactFailed', source, "فشل في حذف جهة الاتصال")
        end
    end)
end)

-- Get player contacts
function GetPlayerContacts(source)
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    exports['mongodb-async']:find("phone_contacts", {owner = identifier}, function(success, contacts)
        if success then
            TriggerClientEvent('phone:updateContacts', source, contacts)
        end
    end)
end

-- Get bank info
RegisterServerEvent('phone:getBankInfo')
AddEventHandler('phone:getBankInfo', function()
    local source = source
    local playerData = exports['core-system']:GetPlayerData(source)
    
    if playerData then
        local bankData = {
            cash = playerData.money or 0,
            bank = playerData.bank or 0,
            crypto = playerData.crypto or 0
        }
        
        TriggerClientEvent('phone:updateBankInfo', source, bankData)
    end
end)

-- Transfer money
RegisterServerEvent('phone:transferMoney')
AddEventHandler('phone:transferMoney', function(targetNumber, amount)
    local source = source
    local senderIdentifier = exports['core-system']:GetPlayerIdentifier(source)
    
    -- Validate amount
    if not amount or amount <= 0 then
        TriggerClientEvent('phone:transferFailed', source, "مبلغ غير صحيح")
        return
    end
    
    -- Check if sender has enough money
    local senderMoney = exports['core-system']:GetPlayerMoney(source, "bank")
    if senderMoney < amount then
        TriggerClientEvent('phone:transferFailed', source, "رصيد غير كافي")
        return
    end
    
    -- Find target player
    local targetSource = nil
    for identifier, number in pairs(phoneNumbers) do
        if number == targetNumber then
            for _, playerId in ipairs(GetPlayers()) do
                if exports['core-system']:GetPlayerIdentifier(playerId) == identifier then
                    targetSource = playerId
                    break
                end
            end
            break
        end
    end
    
    if targetSource then
        -- Transfer money
        if exports['core-system']:RemovePlayerMoney(source, "bank", amount) then
            exports['core-system']:AddPlayerMoney(targetSource, "bank", amount)
            
            TriggerClientEvent('phone:transferSuccess', source, "تم تحويل " .. amount .. "$ بنجاح")
            TriggerClientEvent('phone:receiveTransfer', targetSource, amount, phoneNumbers[senderIdentifier])
            
            -- Log the transfer
            exports['core-system']:Log("money_transfer", "Phone transfer: " .. amount .. "$ to " .. targetNumber, {
                sender = senderIdentifier,
                target = targetNumber,
                amount = amount
            })
        else
            TriggerClientEvent('phone:transferFailed', source, "فشل في التحويل")
        end
    else
        TriggerClientEvent('phone:transferFailed', source, "الرقم غير متاح")
    end
end)

-- Load phone numbers on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Load existing phone numbers from database
        exports['mongodb-async']:find("users", {}, function(success, users)
            if success and users then
                for _, user in ipairs(users) do
                    if user.phone_number then
                        phoneNumbers[user.identifier] = user.phone_number
                    end
                end
                print("^2[PHONE] Loaded " .. #users .. " phone numbers^7")
            end
        end)
    end
end)
