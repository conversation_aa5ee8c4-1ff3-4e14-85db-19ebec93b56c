-- Admin Panel Client
local isAdminPanelOpen = false
local playerPermissions = {}
local isAdmin = false

-- Initialize admin system
CreateThread(function()
    while not exports['core-system']:IsPlayerLoggedIn() do
        Wait(1000)
    end
    
    -- Check admin status
    TriggerServerEvent('admin:checkPermissions')
end)

-- Receive admin permissions
RegisterNetEvent('admin:receivePermissions')
AddEventHandler('admin:receivePermissions', function(permissions, adminStatus)
    playerPermissions = permissions
    isAdmin = adminStatus
    
    if isAdmin then
        exports['core-system']:ShowNotification("مرحباً بك في لوحة الإدارة - اضغط " .. Config.AdminKey .. " للفتح", "success")
    end
end)

-- Key mapping for admin panel
RegisterKeyMapping('openadmin', 'Open Admin Panel', 'keyboard', Config.AdminKey)

RegisterCommand('openadmin', function()
    if isAdmin then
        if isAdminPanelOpen then
            CloseAdminPanel()
        else
            OpenAdminPanel()
        end
    end
end, false)

-- Open admin panel
function OpenAdminPanel()
    if not isAdmin or isAdminPanelOpen then return end
    
    isAdminPanelOpen = true
    
    -- Get online players
    TriggerServerEvent('admin:getOnlinePlayers')
    
    -- Open NUI
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "openAdminPanel",
        permissions = playerPermissions,
        config = {
            quickActions = Config.QuickActions,
            vehicleCategories = Config.VehicleCategories,
            weatherTypes = Config.WeatherTypes,
            banReasons = Config.BanReasons,
            kickReasons = Config.KickReasons
        }
    })
    
    -- Disable some controls while panel is open
    CreateThread(function()
        while isAdminPanelOpen do
            Wait(0)
            DisableControlAction(0, 1, true) -- LookLeftRight
            DisableControlAction(0, 2, true) -- LookUpDown
            DisableControlAction(0, 24, true) -- Attack
            DisableControlAction(0, 257, true) -- Attack2
            DisableControlAction(0, 25, true) -- Aim
            DisableControlAction(0, 263, true) -- MeleeAttack1
        end
    end)
end

-- Close admin panel
function CloseAdminPanel()
    if not isAdminPanelOpen then return end
    
    isAdminPanelOpen = false
    
    -- Close NUI
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "closeAdminPanel"
    })
end

-- Receive online players
RegisterNetEvent('admin:receiveOnlinePlayers')
AddEventHandler('admin:receiveOnlinePlayers', function(players)
    SendNUIMessage({
        action = "updatePlayers",
        players = players
    })
end)

-- NUI Callbacks
RegisterNUICallback('closeAdminPanel', function(data, cb)
    CloseAdminPanel()
    cb('ok')
end)

RegisterNUICallback('kickPlayer', function(data, cb)
    TriggerServerEvent('admin:kickPlayer', data.playerId, data.reason)
    cb('ok')
end)

RegisterNUICallback('banPlayer', function(data, cb)
    TriggerServerEvent('admin:banPlayer', data.playerId, data.reason, data.duration)
    cb('ok')
end)

RegisterNUICallback('teleportToPlayer', function(data, cb)
    TriggerServerEvent('admin:teleportToPlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('teleportPlayerToMe', function(data, cb)
    TriggerServerEvent('admin:teleportPlayerToMe', data.playerId)
    cb('ok')
end)

RegisterNUICallback('spectatePlayer', function(data, cb)
    TriggerServerEvent('admin:spectatePlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('freezePlayer', function(data, cb)
    TriggerServerEvent('admin:freezePlayer', data.playerId, data.freeze)
    cb('ok')
end)

RegisterNUICallback('healPlayer', function(data, cb)
    TriggerServerEvent('admin:healPlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('revivePlayer', function(data, cb)
    TriggerServerEvent('admin:revivePlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('setPlayerJob', function(data, cb)
    TriggerServerEvent('admin:setPlayerJob', data.playerId, data.job, data.grade)
    cb('ok')
end)

RegisterNUICallback('givePlayerMoney', function(data, cb)
    TriggerServerEvent('admin:givePlayerMoney', data.playerId, data.moneyType, data.amount)
    cb('ok')
end)

RegisterNUICallback('removePlayerMoney', function(data, cb)
    TriggerServerEvent('admin:removePlayerMoney', data.playerId, data.moneyType, data.amount)
    cb('ok')
end)

RegisterNUICallback('spawnVehicle', function(data, cb)
    TriggerServerEvent('admin:spawnVehicle', data.model)
    cb('ok')
end)

RegisterNUICallback('deleteVehicle', function(data, cb)
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    if vehicle ~= 0 then
        TriggerServerEvent('admin:deleteVehicle', NetworkGetNetworkIdFromEntity(vehicle))
    else
        exports['core-system']:ShowNotification("يجب أن تكون داخل مركبة", "error")
    end
    cb('ok')
end)

RegisterNUICallback('repairVehicle', function(data, cb)
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    if vehicle ~= 0 then
        SetVehicleFixed(vehicle)
        SetVehicleDeformationFixed(vehicle)
        SetVehicleUndriveable(vehicle, false)
        SetVehicleEngineOn(vehicle, true, true)
        exports['core-system']:ShowNotification("تم إصلاح المركبة", "success")
    else
        exports['core-system']:ShowNotification("يجب أن تكون داخل مركبة", "error")
    end
    cb('ok')
end)

RegisterNUICallback('setWeather', function(data, cb)
    TriggerServerEvent('admin:setWeather', data.weather)
    cb('ok')
end)

RegisterNUICallback('setTime', function(data, cb)
    TriggerServerEvent('admin:setTime', data.hour, data.minute)
    cb('ok')
end)

RegisterNUICallback('sendAnnouncement', function(data, cb)
    TriggerServerEvent('admin:sendAnnouncement', data.message)
    cb('ok')
end)

RegisterNUICallback('executeQuickAction', function(data, cb)
    TriggerServerEvent('admin:executeQuickAction', data.action)
    cb('ok')
end)

RegisterNUICallback('getLogs', function(data, cb)
    TriggerServerEvent('admin:getLogs', data.logType, data.limit)
    cb('ok')
end)

-- Receive logs
RegisterNetEvent('admin:receiveLogs')
AddEventHandler('admin:receiveLogs', function(logs)
    SendNUIMessage({
        action = "updateLogs",
        logs = logs
    })
end)

-- Spectate system
local isSpectating = false
local spectateTarget = nil
local originalCoords = nil

RegisterNetEvent('admin:startSpectate')
AddEventHandler('admin:startSpectate', function(targetId)
    if isSpectating then
        StopSpectating()
    end
    
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetId))
    if targetPed and targetPed ~= 0 then
        local playerPed = PlayerPedId()
        originalCoords = GetEntityCoords(playerPed)
        
        isSpectating = true
        spectateTarget = targetId
        
        -- Make player invisible
        SetEntityVisible(playerPed, false, false)
        SetEntityCollision(playerPed, false, false)
        FreezeEntityPosition(playerPed, true)
        
        -- Start spectating
        NetworkSetInSpectatorMode(true, targetPed)
        
        exports['core-system']:ShowNotification("بدء مراقبة اللاعب - اضغط ESC للخروج", "info")
        
        -- Handle spectate controls
        CreateThread(function()
            while isSpectating do
                Wait(0)
                
                if IsControlJustPressed(0, 200) then -- ESC
                    StopSpectating()
                    break
                end
                
                -- Update spectate target position
                local newTargetPed = GetPlayerPed(GetPlayerFromServerId(spectateTarget))
                if newTargetPed and newTargetPed ~= 0 then
                    NetworkSetInSpectatorMode(true, newTargetPed)
                end
            end
        end)
    end
end)

function StopSpectating()
    if not isSpectating then return end
    
    isSpectating = false
    local playerPed = PlayerPedId()
    
    -- Stop spectating
    NetworkSetInSpectatorMode(false, playerPed)
    
    -- Restore player
    if originalCoords then
        SetEntityCoords(playerPed, originalCoords.x, originalCoords.y, originalCoords.z, false, false, false, true)
    end
    
    SetEntityVisible(playerPed, true, false)
    SetEntityCollision(playerPed, true, true)
    FreezeEntityPosition(playerPed, false)
    
    spectateTarget = nil
    originalCoords = nil
    
    exports['core-system']:ShowNotification("تم إيقاف المراقبة", "info")
end

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isAdminPanelOpen then
            CloseAdminPanel()
        end
        if isSpectating then
            StopSpectating()
        end
    end
end)
