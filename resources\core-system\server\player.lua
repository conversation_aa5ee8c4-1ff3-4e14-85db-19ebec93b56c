-- Player Management System
local Players = {}

-- Get player identifier
function GetPlayerIdentifier(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, identifier in pairs(identifiers) do
        if string.match(identifier, "steam:") then
            return identifier
        end
    end
    return nil
end

-- Player connection
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if not identifier then
        setKickReason("Steam required to join this server")
        return
    end
    
    deferrals.defer()
    deferrals.update("Loading your data...")
    
    -- Check if player exists in database
    exports['core-system']:GetUser(identifier, function(success, userData)
        if success and userData then
            -- Player exists, load data
            Players[source] = userData
            deferrals.done()
            exports['core-system']:Log("player_connect", name .. " connected to server", {
                identifier = identifier,
                name = name,
                source = source
            })
        else
            -- New player, create account
            local newPlayerData = {
                name = name,
                money = Config.DefaultMoney,
                bank = Config.DefaultBank,
                job = "unemployed",
                job_grade = 0
            }
            
            exports['core-system']:CreateUser(identifier, newPlayerData, function(createSuccess)
                if createSuccess then
                    Players[source] = newPlayerData
                    Players[source].identifier = identifier
                    deferrals.done()
                    exports['core-system']:Log("player_create", "New player created: " .. name, {
                        identifier = identifier,
                        name = name,
                        source = source
                    })
                else
                    setKickReason("Failed to create player data")
                end
            end)
        end
    end)
end)

-- Player disconnection
AddEventHandler('playerDropped', function(reason)
    local source = source
    local identifier = GetPlayerIdentifier(source)
    local playerData = Players[source]
    
    if playerData then
        -- Save player data before disconnect
        exports['core-system']:UpdateUser(identifier, playerData, function(success)
            if success then
                print("^2[CORE] Player data saved for: " .. GetPlayerName(source) .. "^7")
            else
                print("^1[CORE] Failed to save player data for: " .. GetPlayerName(source) .. "^7")
            end
        end)
        
        exports['core-system']:Log("player_disconnect", GetPlayerName(source) .. " disconnected: " .. reason, {
            identifier = identifier,
            name = GetPlayerName(source),
            source = source,
            reason = reason
        })
        
        Players[source] = nil
    end
end)

-- Get player data
function GetPlayerData(source)
    return Players[source]
end

-- Save player data
function SavePlayerData(source, data)
    local identifier = GetPlayerIdentifier(source)
    if identifier and data then
        Players[source] = data
        exports['core-system']:UpdateUser(identifier, data)
    end
end

-- Money functions
function GetPlayerMoney(source, moneyType)
    local playerData = Players[source]
    if playerData then
        return playerData[moneyType] or 0
    end
    return 0
end

function AddPlayerMoney(source, moneyType, amount)
    local playerData = Players[source]
    if playerData and amount > 0 then
        playerData[moneyType] = (playerData[moneyType] or 0) + amount
        TriggerClientEvent('core:updateMoney', source, moneyType, playerData[moneyType])
        
        exports['core-system']:Log("money_add", "Added " .. amount .. " " .. moneyType, {
            identifier = GetPlayerIdentifier(source),
            name = GetPlayerName(source),
            source = source,
            amount = amount,
            type = moneyType
        })
        
        return true
    end
    return false
end

function RemovePlayerMoney(source, moneyType, amount)
    local playerData = Players[source]
    if playerData and amount > 0 and (playerData[moneyType] or 0) >= amount then
        playerData[moneyType] = (playerData[moneyType] or 0) - amount
        TriggerClientEvent('core:updateMoney', source, moneyType, playerData[moneyType])
        
        exports['core-system']:Log("money_remove", "Removed " .. amount .. " " .. moneyType, {
            identifier = GetPlayerIdentifier(source),
            name = GetPlayerName(source),
            source = source,
            amount = amount,
            type = moneyType
        })
        
        return true
    end
    return false
end

-- Auto save player data every 5 minutes
CreateThread(function()
    while true do
        Wait(300000) -- 5 minutes
        
        for source, playerData in pairs(Players) do
            local identifier = GetPlayerIdentifier(source)
            if identifier then
                exports['core-system']:UpdateUser(identifier, playerData)
            end
        end
        
        print("^2[CORE] Auto-saved all player data^7")
    end
end)

-- Exports
exports('GetPlayerData', GetPlayerData)
exports('SavePlayerData', SavePlayerData)
exports('GetPlayerMoney', GetPlayerMoney)
exports('AddPlayerMoney', AddPlayerMoney)
exports('RemovePlayerMoney', RemovePlayerMoney)
exports('GetPlayerIdentifier', GetPlayerIdentifier)
