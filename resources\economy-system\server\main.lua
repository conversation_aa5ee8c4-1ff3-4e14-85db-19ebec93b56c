-- Economy System Server
local bankAccounts = {}
local cryptoPrices = {}
local businesses = {}

-- Initialize economy system
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        InitializeEconomy()
        StartEconomyThreads()
    end
end)

-- Initialize economy
function InitializeEconomy()
    -- Load bank accounts
    LoadBankAccounts()
    
    -- Initialize crypto prices
    InitializeCryptoPrices()
    
    -- Load businesses
    LoadBusinesses()
    
    print("^2[ECONOMY] Economy system initialized^7")
end

-- Load bank accounts
function LoadBankAccounts()
    exports['mongodb-async']:find("banking", {}, function(success, accounts)
        if success and accounts then
            for _, account in ipairs(accounts) do
                bankAccounts[account.identifier] = account
            end
            print("^2[ECONOMY] Loaded " .. #accounts .. " bank accounts^7")
        end
    end)
end

-- Initialize crypto prices
function InitializeCryptoPrices()
    for cryptoType, data in pairs(Config.Crypto.Types) do
        cryptoPrices[cryptoType] = Config.Crypto.StartingPrices[cryptoType] or 1000
    end
end

-- Load businesses
function LoadBusinesses()
    exports['mongodb-async']:find("businesses", {}, function(success, businessData)
        if success and businessData then
            for _, business in ipairs(businessData) do
                businesses[business.id] = business
            end
            print("^2[ECONOMY] Loaded " .. #businessData .. " businesses^7")
        end
    end)
end

-- Start economy threads
function StartEconomyThreads()
    -- Salary payment thread
    CreateThread(function()
        while true do
            Wait(3600000) -- 1 hour
            PaySalaries()
        end
    end)
    
    -- Crypto price update thread
    CreateThread(function()
        while true do
            Wait(Config.Crypto.UpdateInterval)
            UpdateCryptoPrices()
        end
    end)
    
    -- Business income thread
    CreateThread(function()
        while true do
            Wait(3600000) -- 1 hour
            ProcessBusinessIncome()
        end
    end)
    
    -- Interest payment thread (daily)
    CreateThread(function()
        while true do
            Wait(********) -- 24 hours
            PayBankInterest()
        end
    end)
end

-- Money functions
function GetPlayerMoney(source, moneyType)
    return exports['core-system']:GetPlayerMoney(source, moneyType)
end

function AddPlayerMoney(source, moneyType, amount, reason)
    if exports['core-system']:AddPlayerMoney(source, moneyType, amount) then
        -- Log transaction
        LogTransaction(source, "add", moneyType, amount, reason or "Unknown")
        
        -- Update bank account if bank money
        if moneyType == "bank" then
            UpdateBankAccount(source, amount)
        end
        
        return true
    end
    return false
end

function RemovePlayerMoney(source, moneyType, amount, reason)
    if exports['core-system']:RemovePlayerMoney(source, moneyType, amount) then
        -- Log transaction
        LogTransaction(source, "remove", moneyType, amount, reason or "Unknown")
        
        -- Update bank account if bank money
        if moneyType == "bank" then
            UpdateBankAccount(source, -amount)
        end
        
        return true
    end
    return false
end

-- Transfer money between players
function TransferMoney(sourcePlayer, targetPlayer, moneyType, amount, reason)
    local sourceMoney = GetPlayerMoney(sourcePlayer, moneyType)
    
    if sourceMoney >= amount then
        if RemovePlayerMoney(sourcePlayer, moneyType, amount, reason) then
            if AddPlayerMoney(targetPlayer, moneyType, amount, reason) then
                -- Apply transfer fee for bank transfers
                if moneyType == "bank" and Config.Banking.TransferFee > 0 then
                    RemovePlayerMoney(sourcePlayer, "bank", Config.Banking.TransferFee, "Transfer fee")
                end
                
                -- Log transfer
                LogTransfer(sourcePlayer, targetPlayer, moneyType, amount, reason)
                
                return true
            else
                -- Refund if target addition failed
                AddPlayerMoney(sourcePlayer, moneyType, amount, "Transfer refund")
            end
        end
    end
    
    return false
end

-- Bank account functions
function GetBankAccount(identifier)
    return bankAccounts[identifier]
end

function CreateBankAccount(identifier, initialBalance)
    local account = {
        identifier = identifier,
        balance = initialBalance or Config.StartingMoney.bank,
        created_at = os.time(),
        last_transaction = os.time(),
        daily_withdraw = 0,
        daily_deposit = 0,
        last_reset = os.date("%Y-%m-%d")
    }
    
    bankAccounts[identifier] = account
    
    -- Save to database
    exports['mongodb-async']:insertOne("banking", account, function(success)
        if success then
            print("^2[ECONOMY] Created bank account for " .. identifier .. "^7")
        end
    end)
    
    return account
end

function UpdateBankAccount(source, amount)
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    if not identifier then return end
    
    local account = bankAccounts[identifier]
    if not account then
        account = CreateBankAccount(identifier)
    end
    
    account.balance = account.balance + amount
    account.last_transaction = os.time()
    
    -- Reset daily limits if new day
    local currentDate = os.date("%Y-%m-%d")
    if account.last_reset ~= currentDate then
        account.daily_withdraw = 0
        account.daily_deposit = 0
        account.last_reset = currentDate
    end
    
    -- Update daily limits
    if amount > 0 then
        account.daily_deposit = account.daily_deposit + amount
    else
        account.daily_withdraw = account.daily_withdraw + math.abs(amount)
    end
    
    -- Save to database
    exports['mongodb-async']:updateOne("banking", {identifier = identifier}, {["$set"] = account})
end

-- Deposit money to bank
RegisterServerEvent('economy:depositMoney')
AddEventHandler('economy:depositMoney', function(amount)
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    if not identifier or amount <= 0 then return end
    
    local account = bankAccounts[identifier] or CreateBankAccount(identifier)
    local cashMoney = GetPlayerMoney(source, "cash")
    
    -- Check daily deposit limit
    if account.daily_deposit + amount > Config.Banking.DepositLimit then
        TriggerClientEvent('economy:notify', source, "تجاوزت الحد اليومي للإيداع", "error")
        return
    end
    
    if cashMoney >= amount then
        if RemovePlayerMoney(source, "cash", amount, "Bank deposit") then
            if AddPlayerMoney(source, "bank", amount, "Bank deposit") then
                TriggerClientEvent('economy:notify', source, "تم إيداع $" .. amount .. " في حسابك", "success")
            end
        end
    else
        TriggerClientEvent('economy:notify', source, "ليس لديك نقد كافي", "error")
    end
end)

-- Withdraw money from bank
RegisterServerEvent('economy:withdrawMoney')
AddEventHandler('economy:withdrawMoney', function(amount)
    local source = source
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    
    if not identifier or amount <= 0 then return end
    
    local account = bankAccounts[identifier] or CreateBankAccount(identifier)
    local bankMoney = GetPlayerMoney(source, "bank")
    
    -- Check daily withdraw limit
    if account.daily_withdraw + amount > Config.Banking.WithdrawLimit then
        TriggerClientEvent('economy:notify', source, "تجاوزت الحد اليومي للسحب", "error")
        return
    end
    
    if bankMoney >= amount then
        if RemovePlayerMoney(source, "bank", amount, "Bank withdrawal") then
            if AddPlayerMoney(source, "cash", amount, "Bank withdrawal") then
                TriggerClientEvent('economy:notify', source, "تم سحب $" .. amount .. " من حسابك", "success")
            end
        end
    else
        TriggerClientEvent('economy:notify', source, "ليس لديك رصيد كافي في البنك", "error")
    end
end)

-- Pay salaries
function PaySalaries()
    for _, playerId in ipairs(GetPlayers()) do
        local playerData = exports['core-system']:GetPlayerData(playerId)
        if playerData and playerData.job then
            local salary = Config.JobSalaries[playerData.job] or 0
            if salary > 0 then
                -- Apply income tax
                local tax = math.floor(salary * Config.Taxes.IncomeTax)
                local netSalary = salary - tax
                
                AddPlayerMoney(playerId, "bank", netSalary, "Salary payment")
                TriggerClientEvent('economy:notify', playerId, "تم دفع راتبك: $" .. netSalary .. " (بعد الضرائب)", "success")
            end
        end
    end
    
    print("^2[ECONOMY] Paid salaries to all online players^7")
end

-- Update crypto prices
function UpdateCryptoPrices()
    for cryptoType, data in pairs(Config.Crypto.Types) do
        local currentPrice = cryptoPrices[cryptoType]
        local change = (math.random() - 0.5) * 2 * data.volatility
        local newPrice = math.max(1, currentPrice * (1 + change))
        
        cryptoPrices[cryptoType] = math.floor(newPrice)
    end
    
    -- Broadcast price update to all players
    TriggerClientEvent('economy:updateCryptoPrices', -1, cryptoPrices)
end

-- Process business income
function ProcessBusinessIncome()
    for businessId, business in pairs(businesses) do
        if business.owner then
            -- Find owner online
            for _, playerId in ipairs(GetPlayers()) do
                local identifier = exports['core-system']:GetPlayerIdentifier(playerId)
                if identifier == business.owner then
                    local income = business.income or 0
                    if income > 0 then
                        AddPlayerMoney(playerId, "bank", income, "Business income: " .. business.name)
                        TriggerClientEvent('economy:notify', playerId, "دخل من " .. business.name .. ": $" .. income, "success")
                    end
                    break
                end
            end
        end
    end
end

-- Pay bank interest
function PayBankInterest()
    for identifier, account in pairs(bankAccounts) do
        if account.balance > Config.Banking.MinBalance then
            local interest = math.floor(account.balance * Config.Banking.InterestRate)
            account.balance = account.balance + interest
            
            -- Update database
            exports['mongodb-async']:updateOne("banking", {identifier = identifier}, {["$set"] = account})
            
            -- Notify if player is online
            for _, playerId in ipairs(GetPlayers()) do
                local playerIdentifier = exports['core-system']:GetPlayerIdentifier(playerId)
                if playerIdentifier == identifier then
                    AddPlayerMoney(playerId, "bank", interest, "Bank interest")
                    TriggerClientEvent('economy:notify', playerId, "فوائد بنكية: $" .. interest, "success")
                    break
                end
            end
        end
    end
    
    print("^2[ECONOMY] Paid bank interest to all accounts^7")
end

-- Logging functions
function LogTransaction(source, type, moneyType, amount, reason)
    local identifier = exports['core-system']:GetPlayerIdentifier(source)
    local logData = {
        identifier = identifier,
        player_name = GetPlayerName(source),
        type = type,
        money_type = moneyType,
        amount = amount,
        reason = reason,
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    exports['mongodb-async']:insertOne("economy_logs", logData)
end

function LogTransfer(sourcePlayer, targetPlayer, moneyType, amount, reason)
    local sourceIdentifier = exports['core-system']:GetPlayerIdentifier(sourcePlayer)
    local targetIdentifier = exports['core-system']:GetPlayerIdentifier(targetPlayer)
    
    local logData = {
        source_identifier = sourceIdentifier,
        source_name = GetPlayerName(sourcePlayer),
        target_identifier = targetIdentifier,
        target_name = GetPlayerName(targetPlayer),
        money_type = moneyType,
        amount = amount,
        reason = reason,
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    exports['mongodb-async']:insertOne("money_transfers", logData)
end

-- Exports
exports('GetPlayerMoney', GetPlayerMoney)
exports('AddPlayerMoney', AddPlayerMoney)
exports('RemovePlayerMoney', RemovePlayerMoney)
exports('TransferMoney', TransferMoney)
exports('GetBankAccount', GetBankAccount)
exports('CreateBankAccount', CreateBankAccount)
