// Modern Phone JavaScript
let phoneData = {
    phoneNumber: null,
    apps: [],
    contacts: [],
    messages: [],
    bankData: {}
};

let currentApp = 'home';
let isPhoneOpen = false;

// Initialize phone
document.addEventListener('DOMContentLoaded', function() {
    updateTime();
    setInterval(updateTime, 1000);
    
    // Add event listeners
    setupEventListeners();
});

// Update time display
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
    });
    document.getElementById('current-time').textContent = timeString;
}

// Setup event listeners
function setupEventListeners() {
    // Dock app clicks
    document.querySelectorAll('.dock-app').forEach(app => {
        app.addEventListener('click', function() {
            const appName = this.getAttribute('data-app');
            openApp(appName);
        });
    });
    
    // App grid clicks
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('app-icon')) {
            const appName = e.target.getAttribute('data-app');
            openApp(appName);
        }
    });
}

// Open app
function openApp(appName) {
    if (appName === currentApp) return;
    
    // Hide current screen
    document.querySelector('.screen.active').classList.remove('active');
    
    // Show new screen
    const newScreen = document.getElementById(appName === 'home' ? 'home-screen' : appName + '-app');
    if (newScreen) {
        newScreen.classList.add('active');
        currentApp = appName;
        
        // Load app data
        loadAppData(appName);
    }
}

// Go to home screen
function goHome() {
    openApp('home');
}

// Load app data
function loadAppData(appName) {
    switch(appName) {
        case 'phone':
            loadPhoneApp();
            break;
        case 'messages':
            loadMessagesApp();
            break;
        case 'contacts':
            loadContactsApp();
            break;
        case 'bank':
            loadBankApp();
            break;
    }
}

// Load phone app
function loadPhoneApp() {
    if (phoneData.phoneNumber) {
        document.getElementById('my-number').textContent = phoneData.phoneNumber;
    }
}

// Load messages app
function loadMessagesApp() {
    const messagesList = document.getElementById('messages-list');
    messagesList.innerHTML = '';
    
    if (phoneData.messages.length === 0) {
        messagesList.innerHTML = '<div class="no-data">لا توجد رسائل</div>';
        return;
    }
    
    phoneData.messages.forEach(message => {
        const messageElement = createMessageElement(message);
        messagesList.appendChild(messageElement);
    });
}

// Load contacts app
function loadContactsApp() {
    const contactsList = document.getElementById('contacts-list');
    contactsList.innerHTML = '';
    
    if (phoneData.contacts.length === 0) {
        contactsList.innerHTML = '<div class="no-data">لا توجد جهات اتصال</div>';
        return;
    }
    
    phoneData.contacts.forEach(contact => {
        const contactElement = createContactElement(contact);
        contactsList.appendChild(contactElement);
    });
}

// Load bank app
function loadBankApp() {
    document.getElementById('bank-balance').textContent = '$' + (phoneData.bankData.bank || 0).toLocaleString();
    document.getElementById('cash-balance').textContent = '$' + (phoneData.bankData.cash || 0).toLocaleString();
}

// Dialer functions
function addDigit(digit) {
    const dialInput = document.getElementById('dial-input');
    if (dialInput.value.length < 10) {
        dialInput.value += digit;
    }
}

function clearInput() {
    const dialInput = document.getElementById('dial-input');
    dialInput.value = dialInput.value.slice(0, -1);
}

function makeCall() {
    const number = document.getElementById('dial-input').value;
    if (number.length >= 8) {
        // Send to FiveM
        fetch(`https://${GetParentResourceName()}/makeCall`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                number: number
            })
        });
        
        // Clear input
        document.getElementById('dial-input').value = '';
        
        // Show calling screen (could be implemented)
        showNotification('جاري الاتصال...', 'info');
    } else {
        showNotification('رقم غير صحيح', 'error');
    }
}

// Create message element
function createMessageElement(message) {
    const div = document.createElement('div');
    div.className = 'message-item';
    div.innerHTML = `
        <div class="message-header">
            <span class="sender-name">${message.senderName || message.senderNumber}</span>
            <span class="message-time">${formatTime(message.timestamp)}</span>
        </div>
        <div class="message-content">${message.message}</div>
    `;
    
    div.addEventListener('click', function() {
        openMessageThread(message.senderNumber, message.senderName);
    });
    
    return div;
}

// Create contact element
function createContactElement(contact) {
    const div = document.createElement('div');
    div.className = 'contact-item';
    div.innerHTML = `
        <div class="contact-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="contact-info">
            <div class="contact-name">${contact.name}</div>
            <div class="contact-number">${contact.number}</div>
        </div>
        <div class="contact-actions">
            <button onclick="callContact('${contact.number}')" class="contact-btn">
                <i class="fas fa-phone"></i>
            </button>
            <button onclick="messageContact('${contact.number}')" class="contact-btn">
                <i class="fas fa-sms"></i>
            </button>
        </div>
    `;
    
    return div;
}

// NUI Message handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openPhone':
            openPhone(data);
            break;
        case 'closePhone':
            closePhone();
            break;
        case 'incomingCall':
            showIncomingCall(data);
            break;
        case 'receiveMessage':
            receiveMessage(data);
            break;
        case 'updateContacts':
            updateContacts(data.contacts);
            break;
        case 'updateBankInfo':
            updateBankInfo(data.bankData);
            break;
    }
});

// Open phone
function openPhone(data) {
    phoneData.phoneNumber = data.phoneNumber;
    phoneData.apps = data.apps || [];
    
    document.getElementById('phone-container').classList.remove('phone-hidden');
    isPhoneOpen = true;
    
    // Load apps grid
    loadAppsGrid();
    
    // Load initial data
    loadAppData(currentApp);
}

// Close phone
function closePhone() {
    document.getElementById('phone-container').classList.add('phone-hidden');
    isPhoneOpen = false;
    currentApp = 'home';
    
    // Reset to home screen
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById('home-screen').classList.add('active');
}

// Load apps grid
function loadAppsGrid() {
    const appsGrid = document.getElementById('apps-grid');
    appsGrid.innerHTML = '';
    
    phoneData.apps.forEach(app => {
        const appElement = document.createElement('div');
        appElement.className = 'app-icon';
        appElement.style.backgroundColor = app.color;
        appElement.setAttribute('data-app', app.app);
        appElement.setAttribute('data-label', app.label);
        appElement.innerHTML = `<i class="${app.icon}"></i>`;
        
        appsGrid.appendChild(appElement);
    });
}

// Show incoming call
function showIncomingCall(data) {
    document.getElementById('caller-name').textContent = data.name;
    document.getElementById('caller-number').textContent = data.number;
    document.getElementById('incoming-call').classList.remove('hidden');
}

// Accept call
function acceptCall() {
    document.getElementById('incoming-call').classList.add('hidden');
    // Handle call acceptance
}

// Decline call
function declineCall() {
    document.getElementById('incoming-call').classList.add('hidden');
    // Handle call decline
}

// Receive message
function receiveMessage(data) {
    phoneData.messages.push({
        senderNumber: data.number,
        senderName: data.name,
        message: data.message,
        timestamp: Date.now() / 1000
    });
    
    // Update messages if currently viewing
    if (currentApp === 'messages') {
        loadMessagesApp();
    }
}

// Update contacts
function updateContacts(contacts) {
    phoneData.contacts = contacts;
    
    // Update contacts if currently viewing
    if (currentApp === 'contacts') {
        loadContactsApp();
    }
}

// Update bank info
function updateBankInfo(bankData) {
    phoneData.bankData = bankData;
    
    // Update bank if currently viewing
    if (currentApp === 'bank') {
        loadBankApp();
    }
}

// Utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Get parent resource name for FiveM
function GetParentResourceName() {
    return window.location.hostname;
}

// Close phone with ESC key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isPhoneOpen) {
        fetch(`https://${GetParentResourceName()}/closePhone`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
    }
});
