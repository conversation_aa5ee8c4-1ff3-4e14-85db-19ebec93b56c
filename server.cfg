# FiveM Server Configuration - Modern Setup
# Server Information
sv_hostname "^1[AR] ^7Modern FiveM Server ^1| ^7العب واستمتع"
sv_maxclients 64
sv_endpointprivacy true

# Server Identity
sv_licenseKey "cfxk_xxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxx"  # Replace with your license key
sv_master1 ""
sv_scriptHookAllowed 0

# Game Settings
sets tags "roleplay,arabic,modern,economy,jobs"
sets banner_detail "https://i.imgur.com/your-banner.png"
sets banner_connecting "https://i.imgur.com/your-connecting.png"

# OneSync Settings (Required for 64+ players)
onesync on

# Steam Web API Key (Optional but recommended)
set steam_webApiKey "your_steam_web_api_key_here"

# Database Configuration
set mongodb_connection_string "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
set mongodb_database "fivem_server"

# Security Settings
set sv_enforceGameBuild 2699
set sv_lan 0
set rcon_password "your_secure_rcon_password"

# Performance Settings
set sv_maxrate 10000
set sv_maxfilesize 50

# Essential Resources
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

# Core Resources
ensure core-system
ensure character-system
ensure economy-system
ensure inventory-system
ensure vehicle-system
ensure property-system
ensure job-system

# UI Resources
ensure phone-system
ensure admin-panel
ensure hud-system
ensure notification-system

# Additional Resources
ensure chat-system
ensure voice-system
ensure weather-system
ensure anti-cheat

# Admin Resources
ensure admin-commands

# Loading Screen
loadscreen_manual_shutdown "yes"
ensure loading-screen

# Server Commands
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.steam:110000100000000 group.admin  # Replace with your Steam ID

# Convars
set temp_convar "hey world!"
set a_convar "some value"

# Discord Integration (Optional)
set discord_webhook "your_discord_webhook_here"

# Logging
set sv_logFile "server.log"
set sv_logLevel "info"
