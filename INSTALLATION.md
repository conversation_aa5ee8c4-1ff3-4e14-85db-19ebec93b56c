# دليل التثبيت - Installation Guide

## متطلبات التشغيل - Requirements

### 1. FiveM Server Files
قم بتحميل ملفات الخادم من:
```
https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/
```

### 2. Node.js (مطلوب لـ MongoDB)
قم بتحميل وتثبيت Node.js من:
```
https://nodejs.org/
```

### 3. MongoDB
اختر إحدى الطرق التالية:

#### الطريقة الأولى: MongoDB Atlas (مجاني - موصى به)
1. اذهب إلى: https://www.mongodb.com/atlas
2. أنشئ حساب مجاني
3. أنشئ cluster جديد
4. احصل على connection string

#### الطريقة الثانية: MongoDB محلي
1. حمل MongoDB من: https://www.mongodb.com/try/download/community
2. ثب<PERSON> MongoDB على جهازك
3. شغل MongoDB service

## خطوات التثبيت - Installation Steps

### الخطوة 1: تحضير الملفات
1. استخرج ملفات FiveM Server في مجلد السيرفر
2. تأكد من وجود الملفات التالية:
   - `FXServer.exe`
   - `server.cfg`
   - `resources/` folder

### الخطوة 2: تثبيت MongoDB Driver
افتح Command Prompt في مجلد `resources/mongodb-async/` وشغل:
```bash
npm install
```

### الخطوة 3: تحديث الإعدادات
قم بتحديث الإعدادات التالية في `server.cfg`:

```cfg
# ضع license key الخاص بك
sv_licenseKey "your_license_key_here"

# ضع Steam Web API Key (اختياري)
set steam_webApiKey "your_steam_web_api_key"

# ضع Steam ID الخاص بك للحصول على صلاحيات الإدارة
add_principal identifier.steam:YOUR_STEAM_ID group.admin

# تأكد من صحة MongoDB connection string
set mongodb_connection_string "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
```

### الخطوة 4: الحصول على License Key
1. اذهب إلى: https://keymaster.fivem.net/
2. سجل دخول بحساب Cfx.re
3. أنشئ server key جديد
4. انسخ الـ key وضعه في server.cfg

### الخطوة 5: الحصول على Steam Web API Key (اختياري)
1. اذهب إلى: https://steamcommunity.com/dev/apikey
2. أنشئ API key جديد
3. انسخ الـ key وضعه في server.cfg

### الخطوة 6: الحصول على Steam ID
1. اذهب إلى: https://steamid.io/
2. ضع رابط Steam profile الخاص بك
3. انسخ الـ SteamID64
4. ضعه في server.cfg بدلاً من YOUR_STEAM_ID

## تشغيل الخادم - Running the Server

### الطريقة الأولى: استخدام run.bat
```bash
run.bat
```

### الطريقة الثانية: يدوياً
```bash
FXServer.exe +exec server.cfg
```

## التحقق من التثبيت - Verification

### 1. تحقق من الاتصال بقاعدة البيانات
ابحث عن هذه الرسائل في console:
```
[MongoDB] Connected to database: fivem_server
[CORE] MongoDB connected successfully!
```

### 2. تحقق من تحميل الموارد
ابحث عن هذه الرسائل:
```
[CORE] Collection 'users' created/verified
[PHONE] Loaded X phone numbers
[ADMIN] Economy system initialized
```

### 3. اختبار الجوال
- ادخل الخادم
- اضغط على حرف `K`
- يجب أن يظهر الجوال

### 4. اختبار لوحة الإدارة
- اضغط على `F1`
- يجب أن تظهر لوحة الإدارة (للإداريين فقط)

## حل المشاكل الشائعة - Troubleshooting

### مشكلة: "MongoDB connection failed"
**الحل:**
1. تحقق من صحة connection string
2. تأكد من أن MongoDB يعمل
3. تحقق من الاتصال بالإنترنت

### مشكلة: "License key invalid"
**الحل:**
1. تأكد من صحة license key
2. تحقق من أن الـ key لم ينته
3. أنشئ key جديد إذا لزم الأمر

### مشكلة: "Resource failed to start"
**الحل:**
1. تحقق من console للأخطاء
2. تأكد من تثبيت جميع dependencies
3. تحقق من صحة ملفات الموارد

### مشكلة: "Admin panel not opening"
**الحل:**
1. تأكد من إضافة Steam ID في server.cfg
2. تحقق من صلاحيات الإدارة
3. أعد تشغيل الخادم

### مشكلة: "Phone not opening"
**الحل:**
1. تحقق من تحميل core-system
2. تحقق من تحميل phone-system
3. تأكد من عدم وجود تضارب في الـ keybinds

## الدعم - Support

إذا واجهت أي مشاكل:
1. تحقق من console للأخطاء
2. راجع هذا الدليل مرة أخرى
3. تأكد من تحديث جميع الإعدادات

## ملاحظات مهمة - Important Notes

1. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من قاعدة البيانات
2. **الأمان**: لا تشارك license key أو API keys
3. **التحديثات**: تحقق من التحديثات بانتظام
4. **الأداء**: راقب استخدام الموارد

---

**تم إنشاء هذا الدليل بواسطة Augment Agent**
