-- Phone System Client
local isPhoneOpen = false
local phoneObject = nil
local phoneNumber = nil

-- Initialize phone system
CreateThread(function()
    while not exports['core-system']:IsPlayerLoggedIn() do
        Wait(1000)
    end
    
    -- Get player phone number
    TriggerServerEvent('phone:getPhoneNumber')
end)

-- Receive phone number from server
RegisterNetEvent('phone:receivePhoneNumber')
AddEventHandler('phone:receivePhoneNumber', function(number)
    phoneNumber = number
end)

-- Toggle phone
RegisterNetEvent('phone:toggle')
AddEventHandler('phone:toggle', function()
    if not exports['core-system']:IsPlayerLoggedIn() then
        return
    end
    
    if isPhoneOpen then
        ClosePhone()
    else
        OpenPhone()
    end
end)

-- Open phone
function OpenPhone()
    if isPhoneOpen then return end
    
    local playerPed = PlayerPedId()
    
    -- Check if player has phone item (if inventory system is available)
    -- TriggerServerEvent('phone:checkPhoneItem')
    
    isPhoneOpen = true
    
    -- Create phone animation
    RequestAnimDict(Config.PhoneAnimation.dict)
    while not HasAnimDictLoaded(Config.PhoneAnimation.dict) do
        Wait(1)
    end
    
    TaskPlayAnim(playerPed, Config.PhoneAnimation.dict, Config.PhoneAnimation.anim, 8.0, -8.0, -1, Config.PhoneAnimation.flag, 0, false, false, false)
    
    -- Create phone prop
    CreatePhoneProp()
    
    -- Open NUI
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "openPhone",
        phoneNumber = phoneNumber,
        apps = Config.Apps
    })
    
    -- Disable some controls while phone is open
    CreateThread(function()
        while isPhoneOpen do
            Wait(0)
            DisableControlAction(0, 1, true) -- LookLeftRight
            DisableControlAction(0, 2, true) -- LookUpDown
            DisableControlAction(0, 24, true) -- Attack
            DisableControlAction(0, 257, true) -- Attack2
            DisableControlAction(0, 25, true) -- Aim
            DisableControlAction(0, 263, true) -- MeleeAttack1
            DisableControlAction(0, 37, true) -- SelectWeapon
            DisableControlAction(0, 44, true) -- Cover
            DisableControlAction(0, 45, true) -- Reload
            DisableControlAction(0, 140, true) -- MeleeAttackLight
            DisableControlAction(0, 141, true) -- MeleeAttackHeavy
            DisableControlAction(0, 142, true) -- MeleeAttackAlternate
            DisableControlAction(0, 143, true) -- MeleeBlock
        end
    end)
end

-- Close phone
function ClosePhone()
    if not isPhoneOpen then return end
    
    isPhoneOpen = false
    
    local playerPed = PlayerPedId()
    
    -- Stop animation
    StopAnimTask(playerPed, Config.PhoneAnimation.dict, Config.PhoneAnimation.anim, 1.0)
    
    -- Remove phone prop
    RemovePhoneProp()
    
    -- Close NUI
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "closePhone"
    })
end

-- Create phone prop
function CreatePhoneProp()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    RequestModel(Config.PhoneProp.model)
    while not HasModelLoaded(Config.PhoneProp.model) do
        Wait(1)
    end
    
    phoneObject = CreateObject(GetHashKey(Config.PhoneProp.model), coords.x, coords.y, coords.z, true, true, true)
    local boneIndex = GetPedBoneIndex(playerPed, Config.PhoneProp.bone)
    
    AttachEntityToEntity(phoneObject, playerPed, boneIndex, 
        Config.PhoneProp.offset[1], Config.PhoneProp.offset[2], Config.PhoneProp.offset[3],
        Config.PhoneProp.rotation[1], Config.PhoneProp.rotation[2], Config.PhoneProp.rotation[3],
        true, true, false, true, 1, true)
end

-- Remove phone prop
function RemovePhoneProp()
    if phoneObject then
        DeleteObject(phoneObject)
        phoneObject = nil
    end
end

-- NUI Callbacks
RegisterNUICallback('closePhone', function(data, cb)
    ClosePhone()
    cb('ok')
end)

RegisterNUICallback('makeCall', function(data, cb)
    TriggerServerEvent('phone:makeCall', data.number)
    cb('ok')
end)

RegisterNUICallback('sendMessage', function(data, cb)
    TriggerServerEvent('phone:sendMessage', data.number, data.message)
    cb('ok')
end)

RegisterNUICallback('addContact', function(data, cb)
    TriggerServerEvent('phone:addContact', data.name, data.number)
    cb('ok')
end)

RegisterNUICallback('deleteContact', function(data, cb)
    TriggerServerEvent('phone:deleteContact', data.id)
    cb('ok')
end)

RegisterNUICallback('getBankInfo', function(data, cb)
    TriggerServerEvent('phone:getBankInfo')
    cb('ok')
end)

RegisterNUICallback('transferMoney', function(data, cb)
    TriggerServerEvent('phone:transferMoney', data.target, data.amount)
    cb('ok')
end)

-- Receive incoming call
RegisterNetEvent('phone:incomingCall')
AddEventHandler('phone:incomingCall', function(callerNumber, callerName)
    SendNUIMessage({
        action = "incomingCall",
        number = callerNumber,
        name = callerName or "Unknown"
    })
end)

-- Receive message
RegisterNetEvent('phone:receiveMessage')
AddEventHandler('phone:receiveMessage', function(senderNumber, message, senderName)
    SendNUIMessage({
        action = "receiveMessage",
        number = senderNumber,
        message = message,
        name = senderName or "Unknown"
    })
    
    -- Show notification if phone is closed
    if not isPhoneOpen then
        exports['core-system']:ShowNotification("رسالة جديدة من " .. (senderName or senderNumber), "info")
    end
end)

-- Update contacts
RegisterNetEvent('phone:updateContacts')
AddEventHandler('phone:updateContacts', function(contacts)
    SendNUIMessage({
        action = "updateContacts",
        contacts = contacts
    })
end)

-- Update bank info
RegisterNetEvent('phone:updateBankInfo')
AddEventHandler('phone:updateBankInfo', function(bankData)
    SendNUIMessage({
        action = "updateBankInfo",
        bankData = bankData
    })
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isPhoneOpen then
            ClosePhone()
        end
    end
end)
