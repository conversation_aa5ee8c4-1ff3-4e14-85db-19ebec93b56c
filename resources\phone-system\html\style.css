/* Modern Phone CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    overflow: hidden;
    direction: rtl;
}

#phone-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.phone-hidden {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
    pointer-events: none;
}

.phone-frame {
    width: 300px;
    height: 600px;
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-radius: 30px;
    padding: 10px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.phone-header {
    height: 30px;
    background: #000;
    border-radius: 20px 20px 0 0;
    position: relative;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 15px;
    color: #fff;
    font-size: 12px;
}

.status-right i {
    margin-left: 5px;
}

.phone-screen {
    height: 540px;
    background: #000;
    border-radius: 0 0 20px 20px;
    position: relative;
    overflow: hidden;
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.screen.active {
    opacity: 1;
    transform: translateX(0);
}

/* Home Screen */
#home-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.wallpaper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23000000" stop-opacity="0.1"/></radialGradient></defs><rect width="100" height="100" fill="url(%23a)"/></svg>');
}

.apps-grid {
    padding: 40px 20px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    position: relative;
    z-index: 1;
}

.app-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.app-icon:hover {
    transform: scale(1.1);
}

.app-icon::after {
    content: attr(data-label);
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
}

.dock {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    border-radius: 25px;
}

.dock-app {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dock-app:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* App Screens */
.app-screen {
    background: #1a1a1a;
    color: #fff;
}

.app-header {
    height: 60px;
    background: #2d2d2d;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #333;
}

.back-btn {
    background: none;
    border: none;
    color: #007AFF;
    font-size: 18px;
    cursor: pointer;
    margin-left: 10px;
}

.app-header h2 {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.app-content {
    height: calc(100% - 60px);
    padding: 20px;
    overflow-y: auto;
}

/* Phone App */
.phone-number-display {
    text-align: center;
    margin-bottom: 30px;
    font-size: 14px;
    color: #888;
}

#my-number {
    color: #007AFF;
    font-weight: bold;
}

.dialer {
    text-align: center;
}

#dial-input {
    width: 100%;
    padding: 15px;
    font-size: 18px;
    text-align: center;
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 10px;
    color: #fff;
    margin-bottom: 20px;
}

.dial-pad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    max-width: 200px;
    margin: 0 auto;
}

.dial-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #2d2d2d;
    border: 1px solid #444;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dial-btn:hover {
    background: #3d3d3d;
}

.call-btn {
    background: #4CAF50;
    border-color: #4CAF50;
}

.call-btn:hover {
    background: #45a049;
}

/* Bank App */
.bank-info {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.balance-card, .cash-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
}

.balance-card h3, .cash-card h3 {
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.8;
}

.balance-amount, .cash-amount {
    font-size: 24px;
    font-weight: bold;
}

.bank-actions {
    display: grid;
    gap: 15px;
}

.bank-btn {
    padding: 15px;
    background: #007AFF;
    border: none;
    border-radius: 10px;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.2s ease;
}

.bank-btn:hover {
    background: #0056b3;
}

/* Phone Footer */
.phone-footer {
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.home-btn {
    background: none;
    border: none;
    cursor: pointer;
}

.home-indicator {
    width: 40px;
    height: 4px;
    background: #fff;
    border-radius: 2px;
    opacity: 0.3;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.call-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 280px;
    height: 500px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 40px 20px;
    color: #fff;
}

.caller-info {
    text-align: center;
}

.caller-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    margin-bottom: 20px;
}

.caller-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.caller-number {
    font-size: 16px;
    opacity: 0.8;
}

.call-actions {
    display: flex;
    gap: 40px;
}

.decline-btn, .accept-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.decline-btn {
    background: #FF3B30;
    color: #fff;
}

.accept-btn {
    background: #4CAF50;
    color: #fff;
}

.decline-btn:hover {
    background: #d32f2f;
}

.accept-btn:hover {
    background: #45a049;
}
