// MongoDB Async Driver for FiveM
const { MongoClient } = require('mongodb');

let client = null;
let database = null;
let isConnected = false;

// Connect to MongoDB
global.exports('connect', async (connectionString, databaseName, callback) => {
    try {
        client = new MongoClient(connectionString, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        
        await client.connect();
        database = client.db(databaseName);
        isConnected = true;
        
        console.log(`^2[MongoDB] Connected to database: ${databaseName}^7`);
        
        if (callback) {
            callback(true);
        }
    } catch (error) {
        console.log(`^1[MongoDB] Connection failed: ${error.message}^7`);
        
        if (callback) {
            callback(false);
        }
    }
});

// Find documents
global.exports('find', async (collection, query, options, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).find(query, options).toArray();
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] Find error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Find one document
global.exports('findOne', async (collection, query, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).findOne(query);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] FindOne error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Insert one document
global.exports('insertOne', async (collection, document, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).insertOne(document);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] InsertOne error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Insert many documents
global.exports('insertMany', async (collection, documents, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).insertMany(documents);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] InsertMany error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Update one document
global.exports('updateOne', async (collection, filter, update, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).updateOne(filter, update);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] UpdateOne error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Update many documents
global.exports('updateMany', async (collection, filter, update, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).updateMany(filter, update);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] UpdateMany error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Delete one document
global.exports('deleteOne', async (collection, filter, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).deleteOne(filter);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] DeleteOne error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Delete many documents
global.exports('deleteMany', async (collection, filter, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).deleteMany(filter);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] DeleteMany error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Create collection
global.exports('createCollection', async (collectionName, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false);
        return;
    }
    
    try {
        await database.createCollection(collectionName);
        if (callback) callback(true);
    } catch (error) {
        // Collection might already exist, which is fine
        if (callback) callback(true);
    }
});

// Drop collection
global.exports('dropCollection', async (collectionName, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false);
        return;
    }
    
    try {
        await database.collection(collectionName).drop();
        if (callback) callback(true);
    } catch (error) {
        console.log(`^1[MongoDB] DropCollection error: ${error.message}^7`);
        if (callback) callback(false);
    }
});

// Count documents
global.exports('count', async (collection, query, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, 0);
        return;
    }
    
    try {
        const result = await database.collection(collection).countDocuments(query);
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] Count error: ${error.message}^7`);
        if (callback) callback(false, 0);
    }
});

// Aggregate
global.exports('aggregate', async (collection, pipeline, callback) => {
    if (!isConnected) {
        console.log('^1[MongoDB] Not connected to database^7');
        if (callback) callback(false, null);
        return;
    }
    
    try {
        const result = await database.collection(collection).aggregate(pipeline).toArray();
        if (callback) callback(true, result);
    } catch (error) {
        console.log(`^1[MongoDB] Aggregate error: ${error.message}^7`);
        if (callback) callback(false, null);
    }
});

// Handle resource stop
on('onResourceStop', (resourceName) => {
    if (resourceName === GetCurrentResourceName() && client) {
        client.close();
        console.log('^3[MongoDB] Connection closed^7');
    }
});
