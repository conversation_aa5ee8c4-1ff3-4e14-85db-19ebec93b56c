<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="admin-panel" class="admin-panel hidden">
        <div class="panel-container">
            <!-- Header -->
            <div class="panel-header">
                <h1><i class="fas fa-shield-alt"></i> لوحة الإدارة</h1>
                <button class="close-btn" onclick="closeAdminPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Navigation -->
            <div class="panel-nav">
                <button class="nav-btn active" data-tab="players">
                    <i class="fas fa-users"></i> اللاعبين
                </button>
                <button class="nav-btn" data-tab="vehicles">
                    <i class="fas fa-car"></i> المركبات
                </button>
                <button class="nav-btn" data-tab="server">
                    <i class="fas fa-server"></i> الخادم
                </button>
                <button class="nav-btn" data-tab="logs">
                    <i class="fas fa-clipboard-list"></i> السجلات
                </button>
                <button class="nav-btn" data-tab="quick">
                    <i class="fas fa-bolt"></i> إجراءات سريعة
                </button>
            </div>

            <!-- Content -->
            <div class="panel-content">
                <!-- Players Tab -->
                <div id="players-tab" class="tab-content active">
                    <div class="content-header">
                        <h2>إدارة اللاعبين</h2>
                        <button class="refresh-btn" onclick="refreshPlayers()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                    
                    <div class="players-grid" id="players-grid">
                        <!-- Players will be loaded here -->
                    </div>
                </div>

                <!-- Vehicles Tab -->
                <div id="vehicles-tab" class="tab-content">
                    <div class="content-header">
                        <h2>إدارة المركبات</h2>
                    </div>
                    
                    <div class="vehicle-actions">
                        <button class="action-btn danger" onclick="deleteCurrentVehicle()">
                            <i class="fas fa-trash"></i> حذف المركبة الحالية
                        </button>
                        <button class="action-btn success" onclick="repairCurrentVehicle()">
                            <i class="fas fa-wrench"></i> إصلاح المركبة الحالية
                        </button>
                    </div>
                    
                    <div class="vehicle-categories" id="vehicle-categories">
                        <!-- Vehicle categories will be loaded here -->
                    </div>
                </div>

                <!-- Server Tab -->
                <div id="server-tab" class="tab-content">
                    <div class="content-header">
                        <h2>إدارة الخادم</h2>
                    </div>
                    
                    <div class="server-controls">
                        <div class="control-group">
                            <h3>الطقس</h3>
                            <select id="weather-select">
                                <!-- Weather options will be loaded here -->
                            </select>
                            <button class="action-btn" onclick="setWeather()">
                                <i class="fas fa-cloud"></i> تغيير الطقس
                            </button>
                        </div>
                        
                        <div class="control-group">
                            <h3>الوقت</h3>
                            <div class="time-inputs">
                                <input type="number" id="hour-input" min="0" max="23" placeholder="الساعة">
                                <input type="number" id="minute-input" min="0" max="59" placeholder="الدقيقة">
                            </div>
                            <button class="action-btn" onclick="setTime()">
                                <i class="fas fa-clock"></i> تغيير الوقت
                            </button>
                        </div>
                        
                        <div class="control-group">
                            <h3>إعلان</h3>
                            <textarea id="announcement-text" placeholder="اكتب الإعلان هنا..."></textarea>
                            <button class="action-btn warning" onclick="sendAnnouncement()">
                                <i class="fas fa-bullhorn"></i> إرسال إعلان
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Logs Tab -->
                <div id="logs-tab" class="tab-content">
                    <div class="content-header">
                        <h2>سجلات الإدارة</h2>
                        <div class="log-controls">
                            <select id="log-type-select">
                                <option value="">جميع السجلات</option>
                                <option value="kick">الطرد</option>
                                <option value="ban">الحظر</option>
                                <option value="teleport">النقل</option>
                                <option value="heal">الشفاء</option>
                                <option value="money">الأموال</option>
                            </select>
                            <button class="action-btn" onclick="loadLogs()">
                                <i class="fas fa-search"></i> تحميل
                            </button>
                        </div>
                    </div>
                    
                    <div class="logs-container" id="logs-container">
                        <!-- Logs will be loaded here -->
                    </div>
                </div>

                <!-- Quick Actions Tab -->
                <div id="quick-tab" class="tab-content">
                    <div class="content-header">
                        <h2>الإجراءات السريعة</h2>
                    </div>
                    
                    <div class="quick-actions" id="quick-actions">
                        <!-- Quick actions will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Action Modal -->
    <div id="player-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-player-name">اللاعب</h3>
                <button class="close-btn" onclick="closePlayerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="player-info" id="player-info">
                    <!-- Player info will be loaded here -->
                </div>
                <div class="player-actions">
                    <button class="action-btn danger" onclick="showKickModal()">
                        <i class="fas fa-sign-out-alt"></i> طرد
                    </button>
                    <button class="action-btn danger" onclick="showBanModal()">
                        <i class="fas fa-ban"></i> حظر
                    </button>
                    <button class="action-btn primary" onclick="teleportToPlayer()">
                        <i class="fas fa-location-arrow"></i> انتقال إليه
                    </button>
                    <button class="action-btn primary" onclick="teleportPlayerToMe()">
                        <i class="fas fa-hand-point-right"></i> جلبه إلي
                    </button>
                    <button class="action-btn warning" onclick="spectatePlayer()">
                        <i class="fas fa-eye"></i> مراقبة
                    </button>
                    <button class="action-btn" onclick="toggleFreeze()">
                        <i class="fas fa-snowflake"></i> تجميد/إلغاء
                    </button>
                    <button class="action-btn success" onclick="healPlayer()">
                        <i class="fas fa-heart"></i> شفاء
                    </button>
                    <button class="action-btn success" onclick="revivePlayer()">
                        <i class="fas fa-user-plus"></i> إحياء
                    </button>
                    <button class="action-btn" onclick="showMoneyModal()">
                        <i class="fas fa-dollar-sign"></i> إدارة الأموال
                    </button>
                    <button class="action-btn" onclick="showJobModal()">
                        <i class="fas fa-briefcase"></i> تغيير الوظيفة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Other modals will be added here -->
    <div id="modals-container"></div>

    <script src="script.js"></script>
</body>
</html>
