Config = {}

-- Database Configuration
Config.MongoDB = {
    ConnectionString = "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
    Database = "fivem_server"
}

-- Server Settings
Config.ServerName = "Modern FiveM Server"
Config.MaxPlayers = 64
Config.DefaultMoney = 5000
Config.DefaultBank = 10000

-- Spawn Settings
Config.DefaultSpawn = {
    x = -269.4,
    y = -955.3,
    z = 31.2,
    heading = 205.8
}

-- Job Settings
Config.Jobs = {
    unemployed = { label = "عاطل", defaultDuty = false, offDutyPay = false },
    police = { label = "شرطة", defaultDuty = true, offDutyPay = false },
    ambulance = { label = "إسعاف", defaultDuty = true, offDutyPay = false },
    mechanic = { label = "ميكانيكي", defaultDuty = true, offDutyPay = false },
    taxi = { label = "تاكسي", defaultDuty = true, offDutyPay = false },
    realestate = { label = "عقارات", defaultDuty = true, offDutyPay = false }
}

-- Money Types
Config.MoneyTypes = {
    cash = { label = "نقد", icon = "fas fa-money-bill-wave" },
    bank = { label = "بنك", icon = "fas fa-credit-card" },
    crypto = { label = "عملة رقمية", icon = "fab fa-bitcoin" }
}

-- Notification Settings
Config.NotificationDuration = 5000

-- Phone Settings
Config.PhoneKey = "k"
Config.PhoneItem = "phone"

-- Admin Settings
Config.AdminGroups = {
    "superadmin",
    "admin",
    "moderator"
}
